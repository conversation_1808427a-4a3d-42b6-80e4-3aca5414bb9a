import { JuziEvent } from '../juzi_event'

describe('JuziEvent', () => {
  describe('isFriendAcceptedEvent', () => {
    it('should validate FriendAcceptedEvent with all required fields including gender', () => {
      const validData = {
        imContactId: 'test-contact-id',
        name: '测试用户',
        avatar: 'https://example.com/avatar.jpg',
        gender: 1, // 1-男，2-女，0-未知
        createTimestamp: Date.now(),
        imInfo: {
          externalUserId: 'external-user-id',
          followUser: {
            wecomUserId: 'wecom-user-id'
          }
        },
        botInfo: {
          botId: 'bot-id',
          imBotId: 'im-bot-id',
          name: 'Bot Name',
          avatar: 'https://example.com/bot-avatar.jpg'
        }
      }

      const result = JuziEvent.isFriendAcceptedEvent(validData)
      expect(result).toBe(true)
    })

    it('should reject data without gender field', () => {
      const invalidData = {
        imContactId: 'test-contact-id',
        name: '测试用户',
        avatar: 'https://example.com/avatar.jpg',
        // gender field is missing
        createTimestamp: Date.now(),
        imInfo: {
          externalUserId: 'external-user-id',
          followUser: {
            wecomUserId: 'wecom-user-id'
          }
        },
        botInfo: {
          botId: 'bot-id',
          imBotId: 'im-bot-id',
          name: 'Bot Name',
          avatar: 'https://example.com/bot-avatar.jpg'
        }
      }

      const result = JuziEvent.isFriendAcceptedEvent(invalidData)
      expect(result).toBe(false)
    })

    it('should reject data with invalid gender type', () => {
      const invalidData = {
        imContactId: 'test-contact-id',
        name: '测试用户',
        avatar: 'https://example.com/avatar.jpg',
        gender: 'male', // should be number, not string
        createTimestamp: Date.now(),
        imInfo: {
          externalUserId: 'external-user-id',
          followUser: {
            wecomUserId: 'wecom-user-id'
          }
        },
        botInfo: {
          botId: 'bot-id',
          imBotId: 'im-bot-id',
          name: 'Bot Name',
          avatar: 'https://example.com/bot-avatar.jpg'
        }
      }

      const result = JuziEvent.isFriendAcceptedEvent(invalidData)
      expect(result).toBe(false)
    })
  })
})
