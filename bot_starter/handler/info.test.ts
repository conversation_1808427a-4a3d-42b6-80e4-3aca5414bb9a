import { ChatStateStore, ChatStatStoreManager } from '../../bot/service/moer/storage/chat_state_store'
import { ChatDB } from '../../bot/service/moer/database/chat'
import { JuziEvent } from './juzi_event'
import { UserSlots } from '../../bot/service/moer/components/flow/helper/slotsExtract'

describe('JuziEvent 测试', () => {
  it('检查现有客户的userSlots', async () => {
    const chatId = '7881301056349177_1688858254705213'

    console.log('=== 检查数据库中的原始数据 ===')

    // 1. 直接从数据库查询
    const chatFromDB = await ChatDB.getById(chatId)
    if (chatFromDB) {
      console.log('数据库中的聊天记录:')
      console.log('- 客户名称:', chatFromDB.contact.wx_name)
      console.log('- 创建时间:', chatFromDB.created_at?.toLocaleString())
      console.log('- 数据库中的userSlots:', JSON.stringify(chatFromDB.chat_state.userSlots, null, 2))
      console.log('- 数据库中的state flags:', JSON.stringify(chatFromDB.chat_state.state, null, 2))
    } else {
      console.log('❌ 数据库中没有找到该聊天记录')
    }

    console.log('\n=== 检查内存中的状态 ===')

    // 2. 强制从数据库重新初始化状态
    await ChatStatStoreManager.initState(chatId, true)

    // 3. 获取内存中的状态
    const slots = ChatStateStore.get(chatId)
    console.log('内存中的完整状态:')
    console.log(JSON.stringify(slots, null, 2))

    console.log('\n=== 检查新增字段 ===')
    console.log('- avatar_analysis:', slots.userSlots.avatar_analysis || '未设置')
    console.log('- gender:', slots.userSlots.gender || '未设置')
    console.log('- wx_nickname:', slots.userSlots.wx_nickname || '未设置')
    console.log('- is_friend_accepted:', slots.state.is_friend_accepted || false)
  }, 600000)

  it('测试头像分析和用户画像更新', async () => {
    console.log('🧪 开始测试头像分析功能...\n')

    // 1. 创建mock数据
    const mockData = {
      imContactId: `test-contact-${Date.now()}`,
      name: '测试用户张三',
      avatar: 'https://zhyt-file.9635.com.cn/ddm/images/1753358919636141.jpg', // 使用一个真实的头像URL进行测试
      gender: 1, // 1-男，2-女，0-未知
      createTimestamp: Date.now(),
      imInfo: {
        externalUserId: `external-user-${Date.now()}`,
        followUser: {
          wecomUserId: `wecom-user-${Date.now()}`
        }
      },
      botInfo: {
        botId: `bot-${Date.now()}`,
        imBotId: `im-bot-${Date.now()}`,
        name: '墨尔冥想助手',
        avatar: 'https://example.com/bot-avatar.jpg'
      }
    }

    const chatId = `test-chat-${Date.now()}_${Date.now()}`
    const userId = `test-user-${Date.now()}`

    console.log('📝 Mock数据:')
    console.log('- 客户名称:', mockData.name)
    console.log('- 性别代码:', mockData.gender)
    console.log('- 头像URL:', mockData.avatar)
    console.log('- 聊天ID:', chatId)

    try {
      // 2. 清理并初始化状态
      ChatStateStore.clear(chatId)
      await ChatStatStoreManager.initState(chatId)

      // 3. 获取更新前的状态
      const beforeState = ChatStateStore.get(chatId)
      const beforeUserSlots = UserSlots.fromRecord(beforeState.moreUserSlots ?? {})
      console.log('\n📊 更新前的moreUserSlots:')
      console.log('- 原始数据:', JSON.stringify(beforeState.moreUserSlots, null, 2))
      console.log('- 格式化显示:', beforeUserSlots.toString())

      // 4. 调用头像分析函数
      console.log('\n🤖 开始调用头像分析函数...')
      await JuziEvent.analyzeAvatarAndUpdateProfile(mockData, chatId, userId)

      // 5. 获取更新后的状态
      const afterState = ChatStateStore.get(chatId)
      const afterUserSlots = UserSlots.fromRecord(afterState.moreUserSlots ?? {})
      console.log('\n📊 更新后的moreUserSlots:')
      console.log('- 原始数据:', JSON.stringify(afterState.moreUserSlots, null, 2))
      console.log('- 格式化显示:', afterUserSlots.toString())

      // 6. 验证更新结果
      console.log('\n🎯 字段验证:')

      // 验证性别字段
      const expectedGender = mockData.gender === 1 ? '男' : mockData.gender === 2 ? '女' : '未知'
      const genderSlot = afterState.moreUserSlots?.['基本信息::性别']
      console.log(`- 性别: ${genderSlot?.content} (期望: ${expectedGender})`)
      expect(genderSlot).toBeDefined()
      expect(genderSlot?.content).toBe(expectedGender)
      expect(genderSlot?.frequency).toBe(1)

      // 验证头像分析字段存在
      const avatarSlot = afterState.moreUserSlots?.['基本信息::头像分析']
      console.log(`- 头像分析: ${avatarSlot ? '✅ 已设置' : '❌ 未设置'}`)
      expect(avatarSlot).toBeDefined()
      expect(avatarSlot?.content).toBeDefined()
      expect(avatarSlot?.content.length).toBeGreaterThan(0)
      expect(avatarSlot?.frequency).toBe(1)

      if (avatarSlot?.content) {
        console.log(`  内容: ${avatarSlot.content}`)
      }

      // 验证UserSlots结构
      expect(afterUserSlots.isTopicExist('基本信息')).toBe(true)
      expect(afterUserSlots.isTopicSubTopicExist('基本信息', '性别')).toBe(true)
      expect(afterUserSlots.isTopicSubTopicExist('基本信息', '头像分析')).toBe(true)

      console.log('\n✅ 测试通过！头像分析功能正常工作')

    } catch (error) {
      console.error('\n❌ 测试失败:', error)
      throw error
    } finally {
      // 清理测试数据
      try {
        ChatStateStore.clear(chatId)
        console.log('\n🧹 测试数据已清理')
      } catch (cleanupError) {
        console.warn('清理测试数据时出现警告:', cleanupError)
      }
    }
  }, 600000)

  it('spsp', async () => {
    await ChatStatStoreManager.initState('7881299704040961_1688858254705213', true)
    const slots = await ChatStateStore.get('7881299704040961_1688858254705213')
    console.log(slots)
  }, 60000)

  it('简单测试头像分析功能（仅内存）', async () => {
    console.log('🧪 开始简单测试头像分析功能...\n')

    // 1. 创建mock数据
    const mockData = {
      imContactId: `simple-test-${Date.now()}`,
      name: '简单测试用户',
      avatar: 'https://zhyt-file.9635.com.cn/ddm/images/1753358919636141.jpg', // 使用一个可靠的头像URL
      gender: 2, // 2-女
      createTimestamp: Date.now(),
      imInfo: {
        externalUserId: 'external-simple-test',
        followUser: {
          wecomUserId: 'wecom-simple-test'
        }
      },
      botInfo: {
        botId: 'bot-simple-test',
        imBotId: 'im-bot-simple-test',
        name: '墨尔冥想助手',
        avatar: 'https://example.com/bot-avatar.jpg'
      }
    }

    const chatId = `simple-test-${Date.now()}_${Date.now()}`
    const userId = `simple-user-${Date.now()}`

    console.log('📝 Mock数据:')
    console.log('- 客户名称:', mockData.name)
    console.log('- 性别代码:', mockData.gender, '(期望: 女)')
    console.log('- 聊天ID:', chatId)

    try {
      // 2. 清理并初始化状态
      ChatStateStore.clear(chatId)
      await ChatStatStoreManager.initState(chatId)

      // 3. 获取初始状态
      const initialState = ChatStateStore.get(chatId)
      const initialUserSlots = UserSlots.fromRecord(initialState.moreUserSlots ?? {})
      console.log('\n📊 初始moreUserSlots:')
      console.log('- 原始数据:', JSON.stringify(initialState.moreUserSlots, null, 2))
      console.log('- 格式化显示:', initialUserSlots.toString())

      // 验证初始状态为空
      expect(Object.keys(initialState.moreUserSlots ?? {})).toHaveLength(0)

      // 4. 调用头像分析函数
      console.log('\n🤖 调用头像分析函数...')
      await JuziEvent.analyzeAvatarAndUpdateProfile(mockData, chatId, userId)

      // 5. 获取更新后的状态
      const updatedState = ChatStateStore.get(chatId)
      const updatedUserSlots = UserSlots.fromRecord(updatedState.moreUserSlots ?? {})
      console.log('\n📊 更新后的moreUserSlots:')
      console.log('- 原始数据:', JSON.stringify(updatedState.moreUserSlots, null, 2))
      console.log('- 格式化显示:', updatedUserSlots.toString())

      // 6. 验证结果
      console.log('\n🎯 验证结果:')

      // 验证性别
      const genderSlot = updatedState.moreUserSlots?.['基本信息::性别']
      expect(genderSlot?.content).toBe('女')
      console.log('✅ 性别字段正确:', genderSlot?.content)

      // 验证微信昵称
      const nicknameSlot = updatedState.moreUserSlots?.['基本信息::微信昵称']
      expect(nicknameSlot?.content).toBe(mockData.name)
      console.log('✅ 微信昵称正确:', nicknameSlot?.content)

      // 验证头像分析存在
      const avatarSlot = updatedState.moreUserSlots?.['基本信息::头像分析']
      expect(avatarSlot).toBeDefined()
      expect(typeof avatarSlot?.content).toBe('string')
      expect(avatarSlot?.content.length).toBeGreaterThan(0)
      console.log('✅ 头像分析已生成:', `${avatarSlot?.content.substring(0, 100)}...`)

      // 验证UserSlots结构
      expect(updatedUserSlots.isTopicExist('基本信息')).toBe(true)
      expect(updatedUserSlots.isTopicSubTopicExist('基本信息', '性别')).toBe(true)
      expect(updatedUserSlots.isTopicSubTopicExist('基本信息', '微信昵称')).toBe(true)
      expect(updatedUserSlots.isTopicSubTopicExist('基本信息', '头像分析')).toBe(true)

      console.log('\n✅ 简单测试通过！')

    } catch (error) {
      console.error('\n❌ 简单测试失败:', error)
      throw error
    } finally {
      // 清理内存状态
      ChatStateStore.clear(chatId)
      console.log('\n🧹 内存状态已清理')
    }
  }, 300000)

  it('测试用户画像合并功能', async () => {
    console.log('🧪 开始测试用户画像合并功能...\n')

    const chatId = `merge-test-${Date.now()}_${Date.now()}`
    const userId = `merge-user-${Date.now()}`

    try {
      // 1. 清理并初始化状态
      ChatStateStore.clear(chatId)
      await ChatStatStoreManager.initState(chatId)

      // 2. 第一次添加用户信息（模拟第一次加好友）
      const firstData = {
        imContactId: `first-${Date.now()}`,
        name: '张三',
        avatar: 'https://zhyt-file.9635.com.cn/ddm/images/1753358919636141.jpg',
        gender: 1, // 男
        createTimestamp: Date.now(),
        imInfo: {
          externalUserId: 'external-first',
          followUser: { wecomUserId: 'wecom-first' }
        },
        botInfo: {
          botId: 'bot-first',
          imBotId: 'im-bot-first',
          name: '墨尔冥想助手',
          avatar: 'https://example.com/bot-avatar.jpg'
        }
      }

      console.log('📝 第一次添加用户信息...')
      await JuziEvent.analyzeAvatarAndUpdateProfile(firstData, chatId, userId)

      const firstState = ChatStateStore.get(chatId)
      const firstUserSlots = UserSlots.fromRecord(firstState.moreUserSlots ?? {})
      console.log('\n📊 第一次添加后的用户画像:')
      console.log(firstUserSlots.toString())

      // 验证第一次添加的结果
      expect(firstUserSlots.isTopicSubTopicExist('基本信息', '性别')).toBe(true)
      expect(firstUserSlots.isTopicSubTopicExist('基本信息', '微信昵称')).toBe(true)
      expect(firstUserSlots.isTopicSubTopicExist('基本信息', '头像分析')).toBe(true)

      // 3. 第二次添加用户信息（模拟更新昵称）
      const secondData = {
        ...firstData,
        name: '张三丰', // 更新昵称
        gender: 1 // 相同性别
      }

      console.log('\n📝 第二次更新用户信息（昵称变更）...')
      await JuziEvent.analyzeAvatarAndUpdateProfile(secondData, chatId, userId)

      const secondState = ChatStateStore.get(chatId)
      const secondUserSlots = UserSlots.fromRecord(secondState.moreUserSlots ?? {})
      console.log('\n📊 第二次更新后的用户画像:')
      console.log(secondUserSlots.toString())

      // 验证合并结果
      const genderSlot = secondState.moreUserSlots?.['基本信息::性别']
      const nicknameSlot = secondState.moreUserSlots?.['基本信息::微信昵称']
      const avatarSlot = secondState.moreUserSlots?.['基本信息::头像分析']

      // 验证性别频次增加
      expect(genderSlot?.frequency).toBe(2) // 应该是2次
      console.log('✅ 性别频次正确:', genderSlot?.frequency)

      // 验证昵称被合并更新
      expect(nicknameSlot?.content).toContain('张三') // 应该包含原来的信息
      expect(nicknameSlot?.frequency).toBe(2) // 频次应该是2
      console.log('✅ 昵称合并正确:', nicknameSlot?.content)

      // 验证头像分析频次增加
      expect(avatarSlot?.frequency).toBe(2)
      console.log('✅ 头像分析频次正确:', avatarSlot?.frequency)

      console.log('\n✅ 合并功能测试通过！')

    } catch (error) {
      console.error('\n❌ 合并功能测试失败:', error)
      throw error
    } finally {
      // 清理内存状态
      ChatStateStore.clear(chatId)
      console.log('\n🧹 内存状态已清理')
    }
  }, 300000)
})