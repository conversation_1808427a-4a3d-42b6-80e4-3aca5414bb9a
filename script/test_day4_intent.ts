#!/usr/bin/env ts-node

import { NewIntentCalculator } from '../bot/service/moer/components/signals/new_intent_score'
import { DataService } from '../bot/service/moer/getter/getData'
import { ChatStatStoreManager } from '../bot/service/moer/storage/chat_state_store'
import { ChatHistoryService } from '../bot/service/moer/components/chat_history/chat_history'

/**
 * 测试第四天意向度计算的脚本（去除下单人群）
 * 用于验证新的第四天计算逻辑
 */

async function testDay4IntentCalculation() {
  try {
    console.log('🚀 开始测试第四天意向度计算（去除下单人群）...\n')

    // 获取测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('❌ 没有找到78期的客户')
      return
    }

    // 测试前3个客户
    const testChats = chats.slice(0, 3)
    
    for (const chat of testChats) {
      console.log(`\n📋 测试客户第四天: ${chat.contact.wx_name} (${chat.id})`)
      console.log('=' .repeat(80))
      
      // 初始化客户状态
      await ChatStatStoreManager.initState(chat.id)
      
      // 获取课程开始时间
      const courseStartTime = await DataService.getCourseStartTime(chat.id)
      console.log(`📅 课程开始时间: ${courseStartTime}`)
      
      // 计算第四天时间范围
      const day4StartTime = new Date(courseStartTime)
      day4StartTime.setDate(day4StartTime.getDate() + 3)
      const day4EndTime = new Date(day4StartTime)
      day4EndTime.setDate(day4EndTime.getDate() + 1)
      
      console.log(`📅 第四天时间范围: ${day4StartTime} 到 ${day4EndTime}`)
      
      // 首先检查是否已下单
      const hasPurchased = await (NewIntentCalculator as any).checkHasPurchased(chat.id)
      console.log(`💰 下单状态: ${hasPurchased ? '已下单' : '未下单'}`)
      
      if (hasPurchased) {
        console.log(`✅ 客户已下单，跳过意向度计算`)
        continue
      }
      
      // 获取聊天历史统计
      const allChatHistory = await ChatHistoryService.getChatHistoryByChatId(chat.id)
      const day4Messages = allChatHistory.filter(msg => {
        const messageTime = new Date(msg.created_at)
        return messageTime >= day4StartTime && messageTime < day4EndTime
      })
      const day4UserMessages = day4Messages.filter(msg => msg.role === 'user')
      
      console.log(`💬 第四天总消息: ${day4Messages.length} 条`)
      console.log(`👤 第四天用户消息: ${day4UserMessages.length} 条`)
      
      // 计算第四天意向度
      try {
        const day4Result = await NewIntentCalculator.calculateCourseIntentScore(chat.id, 4)
        
        console.log(`\n📊 第四天意向度结果:`)
        console.log(`   总分: ${day4Result.intent_score} 分`)
        console.log(`   等级: ${day4Result.intent_level}`)
        console.log(`   基础数据:`)
        console.log(`   - 对话轮数: ${day4Result.course_data?.conversation_rounds}`)
        console.log(`   - 观看分钟数: ${day4Result.course_data?.course_watch_minutes}`)
        console.log(`   - 有反馈: ${day4Result.course_data?.replied_after_course_feeling}`)
        console.log(`   - 已下单: ${day4Result.course_data?.has_purchased}`)
        
        // 获取详细数据
        const day4Data = await (NewIntentCalculator as any).getDay4Data(chat.id)
        
        console.log(`\n🔍 第四天详细分析:`)
        console.log(`   - 对话轮数: ${day4Data.conversationRounds}`)
        console.log(`   - 当天回复: ${day4Data.repliedToday}`)
        console.log(`   - 前一天对话比例: ${day4Data.preDayConversationRatio.toFixed(2)}`)
        console.log(`   - 回复系统班信息: ${day4Data.repliedSystemClassInfo}`)
        console.log(`   - 有负面反馈: ${day4Data.hasNegativeFeedback}`)
        console.log(`   - 完成课程: ${day4Data.courseCompleted}`)
        console.log(`   - 课程反馈: ${day4Data.courseFeedback}`)
        console.log(`   - 提到补课: ${day4Data.mentionedMakeupClass}`)
        
        // 计算各项得分
        console.log(`\n📈 第四天得分明细:`)
        
        // 1. 前一天对话比例
        if (day4Data.preDayConversationRatio > 1) {
          console.log(`   ✅ 前一天对话比例高: +10分`)
        } else {
          console.log(`   ❌ 前一天对话比例低: -10分`)
        }
        
        // 2. 回复系统班相关信息
        if (day4Data.repliedSystemClassInfo) {
          console.log(`   ✅ 回复系统班相关信息: +10分`)
        } else {
          console.log(`   ❌ 未回复系统班相关信息: 0分`)
        }
        
        // 3. 当天回复过信息
        if (day4Data.repliedToday) {
          console.log(`   ✅ 当天回复过信息: +10分`)
        } else {
          console.log(`   ❌ 当天未回复: 0分`)
        }
        
        // 4. 没有负面反馈
        if (!day4Data.hasNegativeFeedback) {
          console.log(`   ✅ 没有负面反馈: +10分`)
        } else {
          console.log(`   ❌ 有负面反馈: 0分`)
        }
        
        // 5. 课程反馈
        const courseFeelingScore = (NewIntentCalculator as any).calculateDay4CourseFeelingScore(day4Data.courseFeedback)
        console.log(`   📝 课程反馈(${day4Data.courseFeedback}): ${courseFeelingScore > 0 ? '+' : ''}${courseFeelingScore}分`)
        
        // 6. 提到补课
        if (day4Data.mentionedMakeupClass) {
          console.log(`   ✅ 提到补课: +10分`)
        } else {
          console.log(`   ❌ 未提到补课: 0分`)
        }
        
        // 7. 看课进度
        if (day4Data.courseWatchMinutes >= 10) {
          console.log(`   ✅ 看课(≥10分钟): +10分`)
        } else {
          console.log(`   ❌ 未看课(<10分钟): 0分`)
        }
        
        if (day4Data.courseCompleted) {
          console.log(`   ✅ 看完: +10分`)
        } else {
          console.log(`   ❌ 未看完: 0分`)
        }
        
        // 根据分数范围显示意向度等级说明
        console.log(`\n📋 意向度等级说明:`)
        if (day4Result.intent_score >= 0 && day4Result.intent_score <= 20) {
          console.log(`   🟡 浅兴趣/结束参与 (0-20分)`)
          console.log(`   定义: 用户在第四天完全关注，可能决定不再参与后续课程。`)
          console.log(`   表现: 用户未参与加课程，缺乏互动，甚至停止了对课程的关注。`)
        } else if (day4Result.intent_score >= 20 && day4Result.intent_score <= 40) {
          console.log(`   🟠 有兴趣/需要最终确认 (20-40分)`)
          console.log(`   定义: 用户对加课程有一定兴趣，但仍在等待最后的确认或额外刺激来推动决策。`)
          console.log(`   表现: 用户参加了加课程，但参与度较低，提问较少，仍需要更多的确认或优惠信息来决定是否继续报名。`)
        } else if (day4Result.intent_score >= 40 && day4Result.intent_score <= 60) {
          console.log(`   🟢 比较积极/需要最终 (40-60分)`)
          console.log(`   定义: 用户对加课程表现出较高的参与度，已接近决定报名，但需要最后的促销或优惠来推动购买决策。`)
          console.log(`   表现: 用户在课程中积极提问，表示了报名的意愿，但仍需要一个最后的激励或优惠来决定。`)
        } else if (day4Result.intent_score >= 60 && day4Result.intent_score <= 80) {
          console.log(`   🔵 高意向/已准备报名 (60-80分)`)
          console.log(`   定义: 用户在第四天表现出强烈的购买意图，几乎没有任何犹豫，准备名后续课程。`)
          console.log(`   表现: 用户在课程中积极提问，表示了强烈的兴趣，表达了报名的意愿，但仍需要一个最后的激励或优惠来决定。`)
        }
        
      } catch (error) {
        console.error(`❌ 计算第四天意向度失败:`, error)
      }
      
      console.log('\n' + '─'.repeat(80))
    }
    
    console.log('\n✅ 第四天意向度测试完成!')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
  }
}

// 运行测试
if (require.main === module) {
  testDay4IntentCalculation().catch(console.error)
}

export { testDay4IntentCalculation }
