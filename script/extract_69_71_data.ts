import { PrismaMongoClient } from '../bot/model/mongodb/prisma'
import { DataService } from '../bot/service/moer/getter/getData'
import { ChatHistoryService } from '../bot/service/moer/components/chat_history/chat_history'
import { MoerAPI } from '../bot/model/moer_api/moer'
import { DanmuDB } from '../bot/service/moer/database/danmu'
import { ChatStateStore } from '../bot/service/moer/storage/chat_state_store'
import { ChatStatStoreManager } from '../bot/service/moer/storage/chat_state_store'
import { getUserId } from '../bot/config/chat_id'
import { DateHelper } from '../bot/lib/date/date'
import * as fs from 'fs'
import * as path from 'path'

interface CustomerData {
  // 基本信息
  course_no: number | null
  customer_nickname: string
  moer_id: string | null

  // 下单信息
  has_ordered: boolean
  order_time: string | null

  // Day1数据
  day1_completion_duration: number | null // 完课时长（秒）
  day1_live_comments: number // 直播间发言次数
  day1_homework_completed: boolean // 作业是否完成
  day1_late_entry: boolean // 是否中途进入（推断）

  // Day2数据
  day2_completion_duration: number | null
  day2_live_comments: number
  day2_homework_completed: boolean
  day2_late_entry: boolean

  // Day3数据
  day3_completion_duration: number | null
  day3_live_comments: number
  day3_homework_completed: boolean
  day3_late_entry: boolean

  // Day4数据
  day4_completion_duration: number | null
  day4_live_comments: number
  day4_homework_completed: boolean
  day4_late_entry: boolean
}

export class CustomerDataExtractor {
  private static prisma = PrismaMongoClient.getInstance()

  /**
   * 拉取69-71期客户数据
   */
  public static async extract69To71Data(
    outputPath: string = './customer_data_69_71.csv',
    concurrency: number = 10
  ): Promise<void> {
    console.log('开始拉取69期到71期的客户数据...')

    const allData: CustomerData[] = []

    // 获取69-71期的所有客户
    const customers = await this.prisma.chat.findMany({
      where: {
        course_no: {
          gte: 69,
          lte: 71
        }
      },
      select: {
        id: true,
        contact: true,
        course_no: true,
        moer_id: true,
        created_at: true,
        chat_state: true
      }
    })

    // 过滤掉内部测试号
    const filteredCustomers = customers.filter(customer =>
      !['Dremo', '班级群任务', '韵如🦁'].includes(customer.contact.wx_name)
    )

    console.log(`找到 ${customers.length} 个客户，过滤后剩余 ${filteredCustomers.length} 个客户`)
    console.log(`使用并发数量: ${concurrency}`)

    // 并发处理客户数据
    const results = await this.processCustomersConcurrently(filteredCustomers, concurrency)

    // 收集所有成功的数据
    results.forEach((result, index) => {
      if (result.success && result.data) {
        allData.push(result.data)
      } else {
        console.error(`处理客户 ${filteredCustomers[index].contact.wx_name} 时出错:`, result.error)
        // 添加空数据，保持数据完整性
        allData.push({
          course_no: filteredCustomers[index].course_no,
          customer_nickname: filteredCustomers[index].contact.wx_name,
          moer_id: filteredCustomers[index].moer_id,
          has_ordered: false,
          order_time: null,
          day1_completion_duration: null,
          day1_live_comments: 0,
          day1_homework_completed: false,
          day1_late_entry: false,
          day2_completion_duration: null,
          day2_live_comments: 0,
          day2_homework_completed: false,
          day2_late_entry: false,
          day3_completion_duration: null,
          day3_live_comments: 0,
          day3_homework_completed: false,
          day3_late_entry: false,
          day4_completion_duration: null,
          day4_live_comments: 0,
          day4_homework_completed: false,
          day4_late_entry: false
        })
      }
    })

    // 生成CSV文件
    await this.generateCSV(allData, outputPath)
    console.log(`数据已导出到: ${outputPath}`)
  }

  /**
   * 并发处理客户数据
   */
  private static async processCustomersConcurrently(
    customers: any[],
    concurrency: number
  ): Promise<Array<{ success: boolean; data?: CustomerData; error?: any }>> {
    const results: Array<{ success: boolean; data?: CustomerData; error?: any }> = []
    let processedCount = 0

    // 分批处理，控制并发数量
    for (let i = 0; i < customers.length; i += concurrency) {
      const batch = customers.slice(i, i + concurrency)
      const batchPromises = batch.map(async (customer, batchIndex) => {
        const globalIndex = i + batchIndex
        const customerName = customer.contact.wx_name

        try {
          console.log(`处理客户 ${globalIndex + 1}/${customers.length}: ${customerName}`)
          const customerData = await this.getCustomerData(customer)
          processedCount++
          console.log(`✅ 完成客户 ${globalIndex + 1}/${customers.length}: ${customerName}`)
          return { success: true, data: customerData }
        } catch (error) {
          processedCount++
          console.error(`❌ 处理客户 ${globalIndex + 1}/${customers.length}: ${customerName} 时出错:`, error)
          return { success: false, error }
        }
      })

      // 等待当前批次完成
      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)

      // 显示进度
      const progress = ((processedCount / customers.length) * 100).toFixed(1)
      console.log(`📊 进度: ${processedCount}/${customers.length} (${progress}%)`)
    }

    return results
  }

  /**
   * 获取单个客户的详细数据
   */
  private static async getCustomerData(customer: any): Promise<CustomerData> {
    const chatId = customer.id
    const moerId = customer.moer_id

    console.log(`  📊 开始处理客户: ${customer.contact.wx_name} (chatId: ${chatId}, moerId: ${moerId})`)

    // 初始化聊天状态
    await ChatStatStoreManager.initState(chatId)

    // 获取下单信息
    const hasOrdered = await this.getOrderStatus(customer.chat_state)
    const orderTime = hasOrdered ? await this.getOrderTime(customer.chat_state) : null
    console.log(`  💰 下单状态: ${hasOrdered ? '是' : '否'}, 下单时间: ${orderTime || '无'}`)

    // 获取各天的数据
    const day1Data = await this.getDayData(chatId, moerId, 1)
    const day2Data = await this.getDayData(chatId, moerId, 2)
    const day3Data = await this.getDayData(chatId, moerId, 3)
    const day4Data = await this.getDayData(chatId, moerId, 4)

    return {
      course_no: customer.course_no,
      customer_nickname: customer.contact.wx_name,
      moer_id: customer.moer_id,
      has_ordered: hasOrdered,
      order_time: orderTime,
      day1_completion_duration: day1Data[`day1_completion_duration`],
      day1_live_comments: day1Data[`day1_live_comments`],
      day1_homework_completed: day1Data[`day1_homework_completed`],
      day1_late_entry: day1Data[`day1_late_entry`],
      day2_completion_duration: day2Data[`day2_completion_duration`],
      day2_live_comments: day2Data[`day2_live_comments`],
      day2_homework_completed: day2Data[`day2_homework_completed`],
      day2_late_entry: day2Data[`day2_late_entry`],
      day3_completion_duration: day3Data[`day3_completion_duration`],
      day3_live_comments: day3Data[`day3_live_comments`],
      day3_homework_completed: day3Data[`day3_homework_completed`],
      day3_late_entry: day3Data[`day3_late_entry`],
      day4_completion_duration: day4Data[`day4_completion_duration`],
      day4_live_comments: day4Data[`day4_live_comments`],
      day4_homework_completed: day4Data[`day4_homework_completed`],
      day4_late_entry: day4Data[`day4_late_entry`]
    }
  }

  /**
   * 获取下单状态
   */
  private static async getOrderStatus(chatState: any): Promise<boolean> {
    try {
      // 直接使用chat_state中的is_complete_payment字段
      return chatState?.state?.is_complete_payment || false
    } catch (error) {
      console.error('获取下单状态失败:', error)
      return false
    }
  }

  /**
   * 获取下单时间
   */
  private static async getOrderTime(chatState: any): Promise<string | null> {
    try {
      // 查找支付完成的事件记录
      const paymentEvent = await this.prisma.event_track.findFirst({
        where: {
          chat_id: chatState.chat_id,
          type: 'PaymentComplete'
        },
        orderBy: {
          timestamp: 'desc'
        }
      })

      return paymentEvent ? paymentEvent.timestamp.toISOString() : null
    } catch (error) {
      console.error('获取下单时间失败:', error)
      return null
    }
  }

  /**
   * 获取指定天的数据
   */
  private static async getDayData(chatId: string, moerId: string | null, day: number): Promise<Record<string, any>> {
    try {
      // 获取完课时长
      const completionDuration = await this.getDayCompletionDuration(chatId, day)
      
      // 获取直播间发言次数
      const liveComments = await this.getDayLiveComments(moerId, day)
      
      // 获取作业完成状态
      const homeworkCompleted = await this.getDayHomeworkCompleted(chatId, day)
      
      // 推断是否中途进入（基于播放时长比例）
      const lateEntry = this.inferLateEntry(completionDuration, day)

      console.log(`    Day${day}: 完课时长=${completionDuration}秒, 发言次数=${liveComments}, 作业完成=${homeworkCompleted}, 中途进入=${lateEntry}`)

      return {
        [`day${day}_completion_duration`]: completionDuration,
        [`day${day}_live_comments`]: liveComments,
        [`day${day}_homework_completed`]: homeworkCompleted,
        [`day${day}_late_entry`]: lateEntry
      }
    } catch (error) {
      console.error(`获取Day${day}数据失败:`, error)
      return {
        [`day${day}_completion_duration`]: null,
        [`day${day}_live_comments`]: 0,
        [`day${day}_homework_completed`]: false,
        [`day${day}_late_entry`]: false
      }
    }
  }

  /**
   * 获取指定天的完课时长
   */
  private static async getDayCompletionDuration(chatId: string, day: number): Promise<number | null> {
    try {
      // 检查是否完成课程
      const isCompleted = await DataService.isCompletedCourse(chatId, { day })
      console.log(`      📚 Day${day}完课状态: ${isCompleted ? '已完成' : '未完成'}`)

      if (!isCompleted) return null

      // 尝试获取实际的观看时长
      const moerId = await DataService.getMoerIdByChatId(chatId)
      if (moerId) {
        try {
          // 使用MoerAPI直接获取课程状态
          const courseMap = await DataService.getMoerCourses(moerId)
          if (courseMap[day] && courseMap[day].live) {
            const courseStatus = await MoerAPI.getUserChapterStatus({
              userId: courseMap[day].live.userId,
              liveId: courseMap[day].live.liveId?.toString(),
              vodId: courseMap[day].live.vodId?.toString(),
              sku: courseMap[day].live.sku
            })
            if (courseStatus && courseStatus.playbackTime) {
              const duration = typeof courseStatus.playbackTime === 'string'
                ? Number(courseStatus.playbackTime)
                : courseStatus.playbackTime
              console.log(`      ⏱️  Day${day}实际观看时长: ${duration}秒`)
              return duration
            }
          }
        } catch (error) {
          console.log(`      ⚠️  获取Day${day}实际观看时长失败，使用默认值`)
        }
      }

      // 返回默认时长
      const defaultDuration = 5400 // 90分钟
      console.log(`      ⏱️  Day${day}使用默认时长: ${defaultDuration}秒`)
      return defaultDuration
    } catch (error) {
      console.error(`获取Day${day}完课时长失败 (chatId: ${chatId}):`, error)
      return null
    }
  }

  /**
   * 获取指定天的直播间发言次数
   */
  private static async getDayLiveComments(moerId: string | null, day: number): Promise<number> {
    if (!moerId) return 0

    try {
      // 获取课程开始时间
      const courseStartTime = await DataService.getCourseStartTimeByCourseNo(69) // 使用69期作为参考
      if (!courseStartTime) return 0

      // 计算指定天的日期
      const dayDate = new Date(courseStartTime)
      dayDate.setDate(dayDate.getDate() + day - 1)
      const formattedDate = DateHelper.formatDate(dayDate, 'YYYY-MM-DD')

      // 获取弹幕数据
      const danmuData = await DanmuDB.getDanmusByMoerId(moerId)
      return danmuData.length
    } catch (error) {
      console.error(`获取Day${day}直播间发言次数失败:`, error)
      return 0
    }
  }

  /**
   * 获取指定天的作业完成状态
   */
  private static async getDayHomeworkCompleted(chatId: string, day: number): Promise<boolean> {
    try {
      const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)
      
      // 查找包含作业关键词的消息
      const homeworkKeywords = ['作业', '打卡', '完成', '做完', '提交']
      const hasHomeworkMessage = chatHistory.some(msg => 
        msg.role === 'user' && 
        homeworkKeywords.some(keyword => msg.content.includes(keyword))
      )

      return hasHomeworkMessage
    } catch (error) {
      console.error(`获取Day${day}作业完成状态失败:`, error)
      return false
    }
  }

  /**
   * 推断是否中途进入（基于播放时长比例）
   */
  private static inferLateEntry(completionDuration: number | null, day: number): boolean {
    if (!completionDuration) return false

    const courseDuration = 5400 // 90分钟课程
    const completionRatio = completionDuration / courseDuration

    // 如果观看时长少于80%，认为是中途进入
    // 或者观看时长少于20分钟（1200秒），也认为是中途进入
    return completionRatio < 0.8 || completionDuration < 1200
  }

  /**
   * 生成CSV文件
   */
  private static async generateCSV(data: CustomerData[], outputPath: string): Promise<void> {
    if (data.length === 0) {
      console.log('没有数据可导出')
      return
    }

    // 定义CSV头部
    const headers = [
      '课程编号',
      '客户昵称',
      '客户是否下单',
      '客户下单时间',
      'day1完课时长',
      'day1直播间发言次数',
      'day1作业是否完成',
      'day1是否中途进入',
      'day2完课时长',
      'day2直播间发言次数',
      'day2作业是否完成',
      'day2是否中途进入',
      'day3完课时长',
      'day3直播间发言次数',
      'day3作业是否完成',
      'day3是否中途进入',
      'day4完课时长',
      'day4直播间发言次数',
      'day4作业是否完成',
      'day4是否中途进入'
    ]

    // 生成CSV内容
    const csvRows = [headers.join(',')]

    for (const row of data) {
      const csvRow = [
        row.course_no || '',
        `"${row.customer_nickname}"`,
        row.has_ordered ? '是' : '否',
        row.order_time || '',
        row.day1_completion_duration || '',
        row.day1_live_comments,
        row.day1_homework_completed ? '是' : '否',
        row.day1_late_entry ? '是' : '否',
        row.day2_completion_duration || '',
        row.day2_live_comments,
        row.day2_homework_completed ? '是' : '否',
        row.day2_late_entry ? '是' : '否',
        row.day3_completion_duration || '',
        row.day3_live_comments,
        row.day3_homework_completed ? '是' : '否',
        row.day3_late_entry ? '是' : '否',
        row.day4_completion_duration || '',
        row.day4_live_comments,
        row.day4_homework_completed ? '是' : '否',
        row.day4_late_entry ? '是' : '否'
      ]
      csvRows.push(csvRow.join(','))
    }

    // 写入文件
    const csvContent = csvRows.join('\n')
    await fs.promises.writeFile(outputPath, csvContent, 'utf8')
  }
}

// 主函数
async function main() {
  try {
    await CustomerDataExtractor.extract69To71Data('./customer_data_69_71.csv', 10)
    console.log('数据提取完成！')
  } catch (error) {
    console.error('数据提取失败:', error)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
} 