import { PrismaMongoClient } from '../bot/model/mongodb/prisma'
import { DataService } from '../bot/service/moer/getter/getData'
import { ChatHistoryService } from '../bot/service/moer/components/chat_history/chat_history'
import { MoerAPI } from '../bot/model/moer_api/moer'
import { DanmuDB } from '../bot/service/moer/database/danmu'
import { ChatStateStore } from '../bot/service/moer/storage/chat_state_store'
import { ChatStatStoreManager } from '../bot/service/moer/storage/chat_state_store'
import { getUserId } from '../bot/config/chat_id'
import { DateHelper } from '../bot/lib/date/date'
import * as fs from 'fs'
import * as path from 'path'

interface CustomerData {
  // 基本信息
  course_no: number | null
  customer_nickname: string
  moer_id: string | null

  // 下单信息
  has_ordered: boolean
  order_time: string | null

  // Day2前对话轮次
  day2_before_conversation_rounds: number

  // Day1数据
  day1_completion_duration: number | null // 完课时长（秒）
  day1_live_comments: number // 直播间发言次数
  day1_homework_completed: boolean // 作业是否完成

  // Day2数据
  day2_conversation_rounds: number
  day2_completion_duration: number | null
  day2_live_comments: number
  day2_homework_completed: boolean

  // Day3数据
  day3_conversation_rounds: number
  day3_completion_duration: number | null
  day3_live_comments: number
  day3_homework_completed: boolean

  // Day4数据
  day4_conversation_rounds: number
  day4_completion_duration: number | null
  day4_live_comments: number
  day4_homework_completed: boolean
}

interface DayData {
  conversation_rounds: number
  completion_duration: number | null
  live_comments: number
  homework_completed: boolean
}

export class MoerDataPanel {
  private static prisma = PrismaMongoClient.getInstance()

  /**
   * 拉取客户数据
   * @param startCourseNo 开始课程编号
   * @param endCourseNo 结束课程编号
   * @param outputPath 输出文件路径
   * @param concurrency 并发数量，默认10
   */
  public static async pullCustomerData(
    startCourseNo: number,
    endCourseNo: number,
    outputPath: string = './customer_data.csv',
    concurrency: number = 20
  ): Promise<void> {
    console.log(`开始拉取课程编号 ${startCourseNo} 到 ${endCourseNo} 的客户数据...`)

    const allData: CustomerData[] = []

    // 获取指定课程编号范围内的所有客户
    const customers = await this.prisma.chat.findMany({
      where: {
        course_no: {
          gte: startCourseNo,
          lte: endCourseNo
        }
      },
      select: {
        id: true,
        contact: true,
        course_no: true,
        moer_id: true,
        created_at: true
      }
    })

    // 过滤掉内部测试号
    const filteredCustomers = customers.filter(customer =>
      !['Dremo', '班级群任务', '韵如🦁'].includes(customer.contact.wx_name)
    )

    console.log(`找到 ${customers.length} 个客户，过滤后剩余 ${filteredCustomers.length} 个客户`)
    console.log(`使用并发数量: ${concurrency}`)

    // 并发处理客户数据
    const results = await this.processCustomersConcurrently(filteredCustomers, concurrency)

    // 收集所有成功的数据
    results.forEach((result, index) => {
      if (result.success && result.data) {
        allData.push(result.data)
      } else {
        console.error(`处理客户 ${filteredCustomers[index].contact.wx_name} 时出错:`, result.error)
        // 添加空数据，保持数据完整性
        allData.push({
          course_no: filteredCustomers[index].course_no,
          customer_nickname: filteredCustomers[index].contact.wx_name,
          moer_id: filteredCustomers[index].moer_id,
          has_ordered: false,
          order_time: null,
          day2_before_conversation_rounds: 0,
          day1_completion_duration: null,
          day1_live_comments: 0,
          day1_homework_completed: false,
          day2_conversation_rounds: 0,
          day2_completion_duration: null,
          day2_live_comments: 0,
          day2_homework_completed: false,
          day3_conversation_rounds: 0,
          day3_completion_duration: null,
          day3_live_comments: 0,
          day3_homework_completed: false,
          day4_conversation_rounds: 0,
          day4_completion_duration: null,
          day4_live_comments: 0,
          day4_homework_completed: false
        })
      }
    })

    // 生成CSV文件
    await this.generateCSV(allData, outputPath)
    console.log(`数据已导出到: ${outputPath}`)
  }

  /**
   * 并发处理客户数据
   */
  private static async processCustomersConcurrently(
    customers: any[],
    concurrency: number
  ): Promise<Array<{ success: boolean; data?: CustomerData; error?: any }>> {
    const results: Array<{ success: boolean; data?: CustomerData; error?: any }> = []
    let processedCount = 0

    // 分批处理，控制并发数量
    for (let i = 0; i < customers.length; i += concurrency) {
      const batch = customers.slice(i, i + concurrency)
      const batchPromises = batch.map(async (customer, batchIndex) => {
        const globalIndex = i + batchIndex
        const customerName = customer.contact.wx_name

        try {
          console.log(`处理客户 ${globalIndex + 1}/${customers.length}: ${customerName}`)
          const customerData = await this.getCustomerData(customer)
          processedCount++
          console.log(`✅ 完成客户 ${globalIndex + 1}/${customers.length}: ${customerName}`)
          return { success: true, data: customerData }
        } catch (error) {
          processedCount++
          console.error(`❌ 处理客户 ${globalIndex + 1}/${customers.length}: ${customerName} 时出错:`, error)
          return { success: false, error }
        }
      })

      // 等待当前批次完成
      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)

      // 显示进度
      const progress = ((processedCount / customers.length) * 100).toFixed(1)
      console.log(`📊 进度: ${processedCount}/${customers.length} (${progress}%)`)
    }

    return results
  }

  /**
   * 获取单个客户的详细数据
   */
  private static async getCustomerData(customer: any): Promise<CustomerData> {
    const chatId = customer.id
    const moerId = customer.moer_id

    console.log(`  📊 开始处理客户: ${customer.contact.wx_name} (chatId: ${chatId}, moerId: ${moerId})`)

    // 初始化聊天状态
    await ChatStatStoreManager.initState(chatId)

    // 获取下单信息
    const hasOrdered = await this.getOrderStatus(chatId)
    const orderTime = hasOrdered ? await this.getOrderTime(chatId) : null
    console.log(`  💰 下单状态: ${hasOrdered ? '是' : '否'}, 下单时间: ${orderTime || '无'}`)

    // 获取Day2前的对话轮次
    const day2BeforeRounds = await this.getDay2BeforeConversationRounds(chatId)
    console.log(`  💬 Day2前对话轮次: ${day2BeforeRounds}`)

    // 获取各天的数据
    const day1Data = await this.getDayData(chatId, moerId, 1)
    const day2Data = await this.getDayData(chatId, moerId, 2)
    const day3Data = await this.getDayData(chatId, moerId, 3)
    const day4Data = await this.getDayData(chatId, moerId, 4)

    console.log(`  📅 Day1: 对话${day1Data.conversation_rounds}轮, 发言${day1Data.live_comments}次, 作业${day1Data.homework_completed ? '完成' : '未完成'}`)
    console.log(`  📅 Day2: 对话${day2Data.conversation_rounds}轮, 发言${day2Data.live_comments}次, 作业${day2Data.homework_completed ? '完成' : '未完成'}`)
    console.log(`  📅 Day3: 对话${day3Data.conversation_rounds}轮, 发言${day3Data.live_comments}次, 作业${day3Data.homework_completed ? '完成' : '未完成'}`)
    console.log(`  📅 Day4: 对话${day4Data.conversation_rounds}轮, 发言${day4Data.live_comments}次, 作业${day4Data.homework_completed ? '完成' : '未完成'}`)

    return {
      course_no: customer.course_no,
      customer_nickname: customer.contact.wx_name,
      moer_id: customer.moer_id,
      has_ordered: hasOrdered,
      order_time: orderTime,
      day2_before_conversation_rounds: day2BeforeRounds,
      day1_completion_duration: day1Data.completion_duration,
      day1_live_comments: day1Data.live_comments,
      day1_homework_completed: day1Data.homework_completed,
      day2_conversation_rounds: day2Data.conversation_rounds,
      day2_completion_duration: day2Data.completion_duration,
      day2_live_comments: day2Data.live_comments,
      day2_homework_completed: day2Data.homework_completed,
      day3_conversation_rounds: day3Data.conversation_rounds,
      day3_completion_duration: day3Data.completion_duration,
      day3_live_comments: day3Data.live_comments,
      day3_homework_completed: day3Data.homework_completed,
      day4_conversation_rounds: day4Data.conversation_rounds,
      day4_completion_duration: day4Data.completion_duration,
      day4_live_comments: day4Data.live_comments,
      day4_homework_completed: day4Data.homework_completed
    }
  }

  /**
   * 获取下单状态
   */
  private static async getOrderStatus(chatId: string): Promise<boolean> {
    try {
      // 方法1: 检查聊天状态
      const state = ChatStateStore.get(chatId)
      if (state.state.is_complete_payment) {
        return true
      }

      // 方法2: 检查事件记录
      const paymentEvent = await this.prisma.event_track.findFirst({
        where: {
          chat_id: chatId,
          type: 'PaymentComplete'
        }
      })
      if (paymentEvent) {
        return true
      }

      // 方法3: 调用DataService
      const isPaid = await DataService.isPaidSystemCourse(chatId)
      return isPaid
    } catch (error) {
      console.error(`获取下单状态失败 (chatId: ${chatId}):`, error)
      return false
    }
  }

  /**
   * 获取下单时间
   */
  private static async getOrderTime(chatId: string): Promise<string | null> {
    try {
      // 查找支付完成的事件记录
      const paymentEvent = await this.prisma.event_track.findFirst({
        where: {
          chat_id: chatId,
          type: 'PaymentComplete'
        },
        orderBy: {
          timestamp: 'desc'
        }
      })

      return paymentEvent ? paymentEvent.timestamp.toISOString() : null
    } catch (error) {
      console.error('获取下单时间失败:', error)
      return null
    }
  }

  /**
   * 获取Day2前的对话轮次
   */
  private static async getDay2BeforeConversationRounds(chatId: string): Promise<number> {
    try {
      const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)
      console.log(`    📝 获取到聊天历史 ${chatHistory.length} 条记录`)

      // 获取课程开始时间
      const courseStartTime = await this.getCourseStartTime(chatId)
      if (!courseStartTime) {
        console.log(`    ⚠️  无法获取课程开始时间，返回0`)
        return 0
      }

      // 计算Day2开始时间（课程开始后的第2天）
      const day2StartTime = DateHelper.add(courseStartTime, 1, 'day')
      console.log(`    📅 课程开始时间: ${courseStartTime.toISOString()}, Day2开始时间: ${day2StartTime.toISOString()}`)

      // 统计Day2前的用户消息数量
      let userMessageCount = 0
      for (const message of chatHistory) {
        if (message.role === 'user' && message.created_at < day2StartTime) {
          userMessageCount++
        }
      }

      console.log(`    💬 Day2前用户消息数量: ${userMessageCount}`)
      return userMessageCount
    } catch (error) {
      console.error(`获取Day2前对话轮次失败 (chatId: ${chatId}):`, error)
      return 0
    }
  }

  /**
   * 获取课程开始时间
   */
  private static async getCourseStartTime(chatId: string): Promise<Date | null> {
    try {
      const chat = await this.prisma.chat.findUnique({
        where: { id: chatId },
        select: { course_no: true }
      })

      if (!chat?.course_no) {
        console.log(`    ⚠️  无法获取课程编号 (chatId: ${chatId})`)
        return null
      }

      // 获取课程信息
      const lesson = await this.prisma.moer_lesson.findFirst({
        where: { lesson_no: chat.course_no }
      })

      if (!lesson?.start_at) {
        console.log(`    ⚠️  无法获取课程开始时间 (courseNo: ${chat.course_no})`)
        return null
      }

      console.log(`    📅 课程 ${chat.course_no} 开始时间: ${lesson.start_at.toISOString()}`)
      return lesson.start_at
    } catch (error) {
      console.error(`获取课程开始时间失败 (chatId: ${chatId}):`, error)
      return null
    }
  }

  /**
   * 获取指定天数的数据
   */
  private static async getDayData(chatId: string, moerId: string | null, day: number): Promise<DayData> {
    try {
      console.log(`    🔍 获取Day${day}数据...`)

      // 获取对话轮次
      const conversationRounds = await this.getDayConversationRounds(chatId, day)

      // 获取完课时长
      const completionDuration = await this.getDayCompletionDuration(chatId, day)

      // 获取直播间发言次数
      const liveComments = await this.getDayLiveComments(moerId, day)

      // 获取作业完成情况
      const homeworkCompleted = await this.getDayHomeworkCompleted(chatId, day)

      return {
        conversation_rounds: conversationRounds,
        completion_duration: completionDuration,
        live_comments: liveComments,
        homework_completed: homeworkCompleted
      }
    } catch (error) {
      console.error(`获取Day${day}数据失败 (chatId: ${chatId}):`, error)
      return {
        conversation_rounds: 0,
        completion_duration: null,
        live_comments: 0,
        homework_completed: false
      }
    }
  }

  /**
   * 获取指定天数的对话轮次
   */
  private static async getDayConversationRounds(chatId: string, day: number): Promise<number> {
    try {
      const courseStartTime = await this.getCourseStartTime(chatId)
      if (!courseStartTime) return 0

      // 计算指定天数的开始和结束时间
      const dayStartTime = DateHelper.add(courseStartTime, day - 1, 'day')
      const dayEndTime = DateHelper.add(courseStartTime, day, 'day')

      const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)

      let userMessageCount = 0
      for (const message of chatHistory) {
        if (message.role === 'user' &&
            message.created_at >= dayStartTime &&
            message.created_at < dayEndTime) {
          userMessageCount++
        }
      }

      console.log(`      💬 Day${day}对话轮次: ${userMessageCount} (时间范围: ${dayStartTime.toISOString()} - ${dayEndTime.toISOString()})`)
      return userMessageCount
    } catch (error) {
      console.error(`获取Day${day}对话轮次失败 (chatId: ${chatId}):`, error)
      return 0
    }
  }

  /**
   * 获取指定天数的完课时长
   */
  private static async getDayCompletionDuration(chatId: string, day: number): Promise<number | null> {
    try {
      // 检查是否完成课程
      const isCompleted = await DataService.isCompletedCourse(chatId, { day })
      console.log(`      📚 Day${day}完课状态: ${isCompleted ? '已完成' : '未完成'}`)

      if (!isCompleted) return null

      // 尝试获取实际的观看时长
      const moerId = await DataService.getMoerIdByChatId(chatId)
      if (moerId) {
        try {
          // 使用MoerAPI直接获取课程状态
          const courseMap = await DataService.getMoerCourses(moerId)
          if (courseMap[day] && courseMap[day].live) {
            const courseStatus = await MoerAPI.getUserChapterStatus({
              userId: courseMap[day].live.userId,
              liveId: courseMap[day].live.liveId?.toString(),
              vodId: courseMap[day].live.vodId?.toString(),
              sku: courseMap[day].live.sku
            })
            if (courseStatus && courseStatus.playbackTime) {
              const duration = typeof courseStatus.playbackTime === 'string'
                ? Number(courseStatus.playbackTime)
                : courseStatus.playbackTime
              console.log(`      ⏱️  Day${day}实际观看时长: ${duration}秒`)
              return duration
            }
          }
        } catch (error) {
          console.log(`      ⚠️  获取Day${day}实际观看时长失败，使用默认值`)
        }
      }

      // 返回默认时长
      const defaultDuration = 5400 // 90分钟
      console.log(`      ⏱️  Day${day}使用默认时长: ${defaultDuration}秒`)
      return defaultDuration
    } catch (error) {
      console.error(`获取Day${day}完课时长失败 (chatId: ${chatId}):`, error)
      return null
    }
  }

  /**
   * 获取指定天数的直播间发言次数
   */
  private static async getDayLiveComments(moerId: string | null, day: number): Promise<number> {
    try {
      if (!moerId) {
        console.log(`      🎤 Day${day}直播间发言: 无moerId，返回0`)
        return 0
      }

      // 获取指定天数的弹幕数据
      const danmus = await DanmuDB.getDanmusByMoerId(moerId)
      console.log(`      🎤 获取到弹幕数据 ${danmus.length} 条`)

      // 过滤指定天数的弹幕
      const dayDanmus = danmus.filter((danmu) => danmu.day === day)
      console.log(`      🎤 Day${day}直播间发言次数: ${dayDanmus.length}`)

      return dayDanmus.length
    } catch (error) {
      console.error(`获取Day${day}直播间发言次数失败 (moerId: ${moerId}):`, error)
      return 0
    }
  }

  /**
   * 获取指定天数的作业完成情况
   */
  private static async getDayHomeworkCompleted(chatId: string, day: number): Promise<boolean> {
    try {
      const state = ChatStateStore.get(chatId)
      let isCompleted = false

      // 根据天数检查作业完成状态
      switch (day) {
        case 1:
          isCompleted = Boolean(state.state.is_complete_day1_homework)
          break
        case 2:
          isCompleted = Boolean(state.state.is_complete_day2_homework)
          break
        case 3:
          isCompleted = Boolean(state.state.is_complete_day3_homework)
          break
        case 4:
          isCompleted = Boolean(state.state.is_complete_day4_homework)
          break
        default:
          isCompleted = false
      }

      console.log(`      📝 Day${day}作业完成状态: ${isCompleted ? '已完成' : '未完成'}`)
      return isCompleted
    } catch (error) {
      console.error(`获取Day${day}作业完成情况失败 (chatId: ${chatId}):`, error)
      return false
    }
  }

  /**
   * 生成CSV文件
   */
  private static async generateCSV(data: CustomerData[], outputPath: string): Promise<void> {
    const headers = [
      '课程编号',
      '客户昵称',
      '客户是否下单',
      '客户下单时间',
      'day2前对话轮次',
      'day1完课时长',
      'day1直播间发言次数',
      'day1作业是否完成',
      'day2对话轮次',
      'day2完课时长',
      'day2直播间发言次数',
      'day2作业是否完成',
      'day3对话轮次',
      'day3完课时长',
      'day3直播间发言次数',
      'day3作业是否完成',
      'day4对话轮次',
      'day4完课时长',
      'day4直播间发言次数',
      'day4作业是否完成'
    ]

    const csvContent = [
      headers.join(','),
      ...data.map((row) => [
        row.course_no || '',
        `"${row.customer_nickname}"`,
        row.has_ordered ? '是' : '否',
        row.order_time || '',
        row.day2_before_conversation_rounds,
        row.day1_completion_duration || '',
        row.day1_live_comments,
        row.day1_homework_completed ? '是' : '否',
        row.day2_conversation_rounds,
        row.day2_completion_duration || '',
        row.day2_live_comments,
        row.day2_homework_completed ? '是' : '否',
        row.day3_conversation_rounds,
        row.day3_completion_duration || '',
        row.day3_live_comments,
        row.day3_homework_completed ? '是' : '否',
        row.day4_conversation_rounds,
        row.day4_completion_duration || '',
        row.day4_live_comments,
        row.day4_homework_completed ? '是' : '否'
      ].join(','))
    ].join('\n')

    // 确保输出目录存在
    const outputDir = path.dirname(outputPath)
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }

    fs.writeFileSync(outputPath, csvContent, 'utf8')
  }
}

// 使用示例
async function main() {
  try {
    // 示例：拉取课程编号1到10的客户数据
    await MoerDataPanel.pullCustomerData(1, 10, './output/customer_data.csv')
  } catch (error) {
    console.error('拉取数据失败:', error)
  }
}

it('sp', async () => {
  await MoerDataPanel.pullCustomerData(65, 65, './customer_data.csv')
}, 6000000)