#!/usr/bin/env ts-node

/**
 * 生成累积意向度CSV报告的快速脚本
 * 使用方法: ts-node script/generate_intent_csv.ts [期数]
 */

import { NewIntentCalculator } from '../bot/service/moer/components/signals/new_intent_score'
import { DataService } from '../bot/service/moer/getter/getData'
import { ChatStatStoreManager } from '../bot/service/moer/storage/chat_state_store'
import * as fs from 'fs'
import * as path from 'path'

async function generateIntentCSV() {
  try {
    // 从命令行参数获取期数，默认为78
    const courseNo = parseInt(process.argv[2]) || 78
    const maxCustomers = parseInt(process.argv[3]) || 10 // 默认处理10个客户

    console.log(`🚀 开始生成第${courseNo}期客户累积意向度CSV报告...`)
    console.log(`📊 最多处理 ${maxCustomers} 个客户`)

    // 创建输出目录
    const outputDir = path.join(process.cwd(), 'output')
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }

    // 获取客户列表
    const chats = await DataService.getChatsByCourseNo(courseNo)
    console.log(`📋 找到 ${chats.length} 个客户`)

    if (chats.length === 0) {
      console.log(`❌ 没有找到第${courseNo}期的客户`)
      return
    }

    // 限制处理数量
    const limitedChats = chats.slice(0, maxCustomers)
    console.log(`📝 实际处理 ${limitedChats.length} 个客户`)

    // 准备CSV文件
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
    const csvFilePath = path.join(outputDir, `course_${courseNo}_intent_scores_${timestamp}.csv`)

    // CSV表头
    const headers = [
      '客户ID',
      '客户姓名',
      '微信名',
      '接量期分数',
      '接量期等级',
      '第1天增量',
      '第1天累积',
      '第1天等级',
      '第2天增量',
      '第2天累积',
      '第2天等级',
      '第3天增量',
      '第3天累积',
      '第3天等级',
      '第4天增量',
      '第4天累积',
      '第4天等级',
      '总增量',
      '是否下单'
    ]

    let csvContent = headers.join(',') + '\n'

    // 处理每个客户
    for (let i = 0; i < limitedChats.length; i++) {
      const chat = limitedChats[i]
      const progress = `${i + 1}/${limitedChats.length}`

      console.log(`\n[${progress}] 处理客户: ${chat.contact.wx_name || chat.contact.name || chat.id}`)

      try {
        // 初始化客户状态
        await ChatStatStoreManager.initState(chat.id)

        // 计算接量期意向度
        const preCourseResult = await NewIntentCalculator.calculatePreCourseIntentScore(chat.id)
        console.log(`  📊 接量期: ${preCourseResult.intent_score}分 (${preCourseResult.intent_level})`)

        // 存储结果
        const results: {
          preCourse: any,
          days: any[],
          increments: number[],
          hasPurchased: boolean
        } = {
          preCourse: preCourseResult,
          days: [],
          increments: [],
          hasPurchased: false
        }

        // 计算各天数据
        for (let day = 1; day <= 4; day++) {
          try {
            // 计算累积意向度
            const cumulativeResult = await NewIntentCalculator.calculateCumulativeIntentScore(chat.id, day)

            // 计算增量
            const dayIncrement = await (NewIntentCalculator as any).calculateSingleDayIntentScore(chat.id, day)

            // 检查是否下单（仅第4天）
            if (day === 4) {
              results.hasPurchased = await (NewIntentCalculator as any).checkHasPurchased(chat.id)
            }

            results.days.push(cumulativeResult)
            results.increments.push(dayIncrement.intent_score as number)

            console.log(`  📈 第${day}天: 增量${dayIncrement.intent_score}, 累积${cumulativeResult.intent_score} (${cumulativeResult.intent_level})`)

          } catch (error) {
            console.log(`  ❌ 第${day}天计算失败: ${error instanceof Error ? error.message : String(error)}`)
            results.days.push({ intent_score: 'ERROR', intent_level: 'ERROR' })
            results.increments.push(0)
          }
        }

        // 计算总增量
        const totalIncrement = results.increments.reduce((sum, inc) => sum + inc, 0)

        // 构建CSV行
        const csvRow = [
          chat.id,
          `"${chat.contact.name || ''}"`,
          `"${chat.contact.wx_name || ''}"`,
          results.preCourse.intent_score,
          `"${results.preCourse.intent_level}"`,
          results.increments[0] || 0,
          results.days[0]?.intent_score || 'ERROR',
          `"${results.days[0]?.intent_level || 'ERROR'}"`,
          results.increments[1] || 0,
          results.days[1]?.intent_score || 'ERROR',
          `"${results.days[1]?.intent_level || 'ERROR'}"`,
          results.increments[2] || 0,
          results.days[2]?.intent_score || 'ERROR',
          `"${results.days[2]?.intent_level || 'ERROR'}"`,
          results.increments[3] || 0,
          results.days[3]?.intent_score || 'ERROR',
          `"${results.days[3]?.intent_level || 'ERROR'}"`,
          totalIncrement,
          results.hasPurchased ? 'YES' : 'NO'
        ]

        csvContent += csvRow.join(',') + '\n'

        console.log(`  ✅ 处理完成，总增量: ${totalIncrement}分`)

      } catch (error) {
        console.error(`  ❌ 客户处理失败: ${error instanceof Error ? error.message : String(error)}`)

        // 添加错误行
        const errorRow = [
          chat.id,
          `"${chat.contact.name || ''}"`,
          `"${chat.contact.wx_name || ''}"`,
          'ERROR', 'ERROR', 'ERROR', 'ERROR', 'ERROR',
          'ERROR', 'ERROR', 'ERROR', 'ERROR', 'ERROR', 'ERROR',
          'ERROR', 'ERROR', 'ERROR', 'ERROR', 'ERROR'
        ]
        csvContent += errorRow.join(',') + '\n'
      }
    }

    // 写入CSV文件
    fs.writeFileSync(csvFilePath, csvContent, 'utf8')

    console.log(`\n✅ CSV报告生成完成!`)
    console.log(`📁 文件路径: ${csvFilePath}`)
    console.log(`📊 处理客户数: ${limitedChats.length}`)
    console.log(`📋 文件大小: ${(fs.statSync(csvFilePath).size / 1024).toFixed(2)} KB`)

    // 显示前几行预览
    console.log(`\n📋 CSV内容预览:`)
    const lines = csvContent.split('\n').slice(0, 4)
    lines.forEach((line, index) => {
      if (line.trim()) {
        console.log(`${index === 0 ? '表头' : `客户${index}`}: ${line.length > 100 ? line.substring(0, 100) + '...' : line}`)
      }
    })

    console.log(`\n🎉 报告生成完成! 可以用Excel或其他工具打开CSV文件查看详细数据。`)

  } catch (error) {
    console.error('❌ 生成CSV报告失败:', error)
    process.exit(1)
  }
}

// 运行脚本
if (require.main === module) {
  console.log('📊 累积意向度CSV生成器')
  console.log('使用方法: ts-node script/generate_intent_csv.ts [期数] [客户数量]')
  console.log('例如: ts-node script/generate_intent_csv.ts 78 15\n')

  generateIntentCSV().catch(console.error)
}

export { generateIntentCSV }
