#!/usr/bin/env ts-node

/**
 * 生成累积意向度CSV报告的快速脚本
 * 使用方法: ts-node script/generate_intent_csv.ts [期数]
 */

import { NewIntentCalculator } from '../bot/service/moer/components/signals/new_intent_score'
import { DataService } from '../bot/service/moer/getter/getData'
import { ChatStatStoreManager } from '../bot/service/moer/storage/chat_state_store'
import * as fs from 'fs'
import * as path from 'path'

async function generateIntentCSV() {
  try {
    // 从命令行参数获取期数，默认为78
    const courseNo = parseInt(process.argv[2], 10) || 78

    console.log(`🚀 开始生成第${courseNo}期客户累积意向度CSV报告...`)

    // 创建输出目录
    const outputDir = path.join(process.cwd(), 'output')
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }

    // 获取客户列表
    const chats = await DataService.getChatsByCourseNo(courseNo)
    console.log(`📋 找到 ${chats.length} 个客户`)

    if (chats.length === 0) {
      console.log(`❌ 没有找到第${courseNo}期的客户`)
      return
    }

    // 准备CSV文件
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
    const csvFilePath = path.join(outputDir, `course_${courseNo}_intent_scores_${timestamp}.csv`)

    // CSV表头
    const headers = [
      '客户ID',
      '微信名',
      '接量期分数',
      '接量期等级',
      '第1天增量',
      '第1天累积',
      '第2天增量',
      '第2天累积',
      '第3天增量',
      '第3天累积',
      '第4天增量',
      '第4天累积',
      '总增量',
      '是否下单'
    ]

    let csvContent = `${headers.join(',')  }\n`

    // 并行处理客户（分批处理以控制并发数）
    const batchSize = 50 // 并发处理50个客户
    const results: string[] = []

    for (let batchStart = 0; batchStart < chats.length; batchStart += batchSize) {
      const batch = chats.slice(batchStart, batchStart + batchSize)
      const batchNumber = Math.floor(batchStart / batchSize) + 1
      const totalBatches = Math.ceil(chats.length / batchSize)

      console.log(`\n🚀 处理第 ${batchNumber}/${totalBatches} 批客户 (${batch.length} 个客户)...`)

      // 并行处理当前批次的所有客户
      const batchPromises = batch.map(async (chat, index) => {
        const globalIndex = batchStart + index + 1
        const chatId = chat.id
        const chatName = chat.contact.wx_name || chat.id

        try {
          console.log(`[${globalIndex}/${chats.length}] 开始处理: ${chatName}`)

          // 初始化客户状态
          await ChatStatStoreManager.initState(chatId)

          // 计算接量期意向度
          const preCourseResult = await NewIntentCalculator.calculatePreCourseIntentScore(chatId)

          // 存储结果
          const customerResults: {
            preCourse: any,
            days: any[],
            increments: number[],
            hasPurchased: boolean
          } = {
            preCourse: preCourseResult,
            days: [],
            increments: [],
            hasPurchased: false
          }

          // 计算各天数据
          for (let day = 1; day <= 4; day++) {
            try {
              // 计算累积意向度
              const cumulativeResult = await NewIntentCalculator.calculateCumulativeIntentScore(chatId, day)

              // 计算增量
              const dayIncrement = await (NewIntentCalculator as any).calculateSingleDayIntentScore(chatId, day)

              // 检查是否下单（仅第4天）
              if (day === 4) {
                customerResults.hasPurchased = await (NewIntentCalculator as any).checkHasPurchased(chatId)
              }

              customerResults.days.push(cumulativeResult)
              customerResults.increments.push(dayIncrement.intent_score as number)

            } catch (error) {
              customerResults.days.push({ intent_score: 'ERROR', intent_level: 'ERROR' })
              customerResults.increments.push(0)
            }
          }

          // 计算总增量
          const totalIncrement = customerResults.increments.reduce((sum, inc) => sum + inc, 0)

          // 构建CSV行
          const csvRow = [
            chatId,
            `"${chatName}"`,
            customerResults.preCourse.intent_score,
            `"${customerResults.preCourse.intent_level}"`,
            customerResults.increments[0] || 0,
            customerResults.days[0]?.intent_score || 'ERROR',
            `"${customerResults.days[0]?.intent_level || 'ERROR'}"`,
            customerResults.increments[1] || 0,
            customerResults.days[1]?.intent_score || 'ERROR',
            `"${customerResults.days[1]?.intent_level || 'ERROR'}"`,
            customerResults.increments[2] || 0,
            customerResults.days[2]?.intent_score || 'ERROR',
            `"${customerResults.days[2]?.intent_level || 'ERROR'}"`,
            customerResults.increments[3] || 0,
            customerResults.days[3]?.intent_score || 'ERROR',
            `"${customerResults.days[3]?.intent_level || 'ERROR'}"`,
            totalIncrement,
            customerResults.hasPurchased ? 'YES' : 'NO'
          ]

          console.log(`[${globalIndex}/${chats.length}] ✅ ${chatName}: 接量期${customerResults.preCourse.intent_score}, 总增量${totalIncrement}`)

          return csvRow.join(',')

        } catch (error) {
          console.error(`[${globalIndex}/${chats.length}] ❌ ${chatName}: ${error instanceof Error ? error.message : String(error)}`)

          // 返回错误行
          const errorRow = [
            chatId,
            `"${chatName}"`,
            'ERROR', 'ERROR', 'ERROR', 'ERROR', 'ERROR',
            'ERROR', 'ERROR', 'ERROR', 'ERROR', 'ERROR', 'ERROR',
            'ERROR', 'ERROR', 'ERROR', 'ERROR', 'ERROR'
          ]
          return errorRow.join(',')
        }
      })

      // 等待当前批次完成
      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)

      console.log(`✅ 第 ${batchNumber}/${totalBatches} 批处理完成 (${batchResults.length} 个客户)`)
    }

    // 组装最终CSV内容
    csvContent += `${results.join('\n')  }\n`

    // 写入CSV文件
    fs.writeFileSync(csvFilePath, csvContent, 'utf8')

    console.log('\n✅ CSV报告生成完成!')
    console.log(`📁 文件路径: ${csvFilePath}`)
    console.log(`📊 处理客户数: ${chats.length}`)
    console.log(`📋 文件大小: ${(fs.statSync(csvFilePath).size / 1024).toFixed(2)} KB`)

    // 显示前几行预览
    console.log('\n📋 CSV内容预览:')
    const lines = csvContent.split('\n').slice(0, 4)
    lines.forEach((line, index) => {
      if (line.trim()) {
        console.log(`${index === 0 ? '表头' : `客户${index}`}: ${line.length > 100 ? `${line.substring(0, 100)  }...` : line}`)
      }
    })

    console.log('\n🎉 报告生成完成! 可以用Excel或其他工具打开CSV文件查看详细数据。')

  } catch (error) {
    console.error('❌ 生成CSV报告失败:', error)
    process.exit(1)
  }
}

// 运行脚本
if (require.main === module) {
  console.log('📊 累积意向度CSV生成器')
  console.log('使用方法: ts-node script/generate_intent_csv.ts [期数] [客户数量]')
  console.log('例如: ts-node script/generate_intent_csv.ts 78 15\n')

  generateIntentCSV().catch(console.error)
}

export { generateIntentCSV }
