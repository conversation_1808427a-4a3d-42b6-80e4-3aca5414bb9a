#!/usr/bin/env ts-node

/**
 * 并行生成累积意向度CSV报告的脚本
 * 专门用于处理大量客户（如1000+）
 * 使用方法: ts-node script/generate_intent_csv_parallel.ts [期数] [客户数量] [并发数]
 */

import { NewIntentCalculator } from '../bot/service/moer/components/signals/new_intent_score'
import { DataService } from '../bot/service/moer/getter/getData'
import { ChatStatStoreManager } from '../bot/service/moer/storage/chat_state_store'
import * as fs from 'fs'
import * as path from 'path'

async function generateIntentCSVParallel() {
  try {
    // 从命令行参数获取配置
    const courseNo = parseInt(process.argv[2], 10) || 78
    const concurrency = parseInt(process.argv[4], 10) || 50   // 默认并发50个

    console.log(`🚀 并行生成第${courseNo}期客户累积意向度CSV报告`)
    console.log(`⚡ 并发数: ${concurrency} 个`)
    console.log(`⏰ 开始时间: ${new Date().toLocaleString()}\n`)

    // 创建输出目录
    const outputDir = path.join(process.cwd(), 'output')
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }

    // 获取客户列表
    const chats = await DataService.getChatsByCourseNo(courseNo)
    console.log(`📋 找到 ${chats.length} 个客户`)

    if (chats.length === 0) {
      console.log(`❌ 没有找到第${courseNo}期的客户`)
      return
    }

    // 准备CSV文件
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
    const csvFilePath = path.join(outputDir, `course_${courseNo}_parallel_${chats.length}customers_${timestamp}.csv`)

    // CSV表头
    const headers = [
      '客户ID',
      '微信名',
      '接量期分数',
      '接量期等级',
      '第1天增量',
      '第1天累积',
      '第2天增量',
      '第2天累积',
      '第3天增量',
      '第3天累积',
      '第4天增量',
      '第4天累积',
      '总增量',
      '是否下单',
      '处理状态'
    ]

    // 写入表头
    let csvContent = `${headers.join(',')  }\n`

    // 分批并行处理
    const results: string[] = []
    const totalBatches = Math.ceil(chats.length / concurrency)

    for (let batchStart = 0; batchStart < chats.length; batchStart += concurrency) {
      const batch = chats.slice(batchStart, batchStart + concurrency)
      const batchNumber = Math.floor(batchStart / concurrency) + 1

      console.log(`🚀 处理第 ${batchNumber}/${totalBatches} 批客户 (${batch.length} 个客户)`)
      console.log(`   范围: ${batchStart + 1} - ${batchStart + batch.length}`)

      const batchStartTime = Date.now()

      // 并行处理当前批次的所有客户
      const batchPromises = batch.map(async (chat, index) => {
        const globalIndex = batchStart + index + 1
        const chatId = chat.id
        const chatName = chat.contact.wx_name || chat.id

        try {
          // 初始化客户状态
          await ChatStatStoreManager.initState(chatId)

          // 计算接量期意向度
          const preCourseResult = await NewIntentCalculator.calculatePreCourseIntentScore(chatId)

          // 存储结果
          const customerResults: {
            preCourse: any,
            days: any[],
            increments: number[],
            hasPurchased: boolean
          } = {
            preCourse: preCourseResult,
            days: [],
            increments: [],
            hasPurchased: false
          }

          // 计算各天数据
          for (let day = 1; day <= 4; day++) {
            try {
              // 计算累积意向度
              const cumulativeResult = await NewIntentCalculator.calculateCumulativeIntentScore(chatId, day)

              // 计算增量
              const dayIncrement = await (NewIntentCalculator as any).calculateSingleDayIntentScore(chatId, day)

              // 检查是否下单（仅第4天）
              if (day === 4) {
                customerResults.hasPurchased = await (NewIntentCalculator as any).checkHasPurchased(chatId)
              }

              customerResults.days.push(cumulativeResult)
              customerResults.increments.push(dayIncrement.intent_score as number)

            } catch (error) {
              customerResults.days.push({ intent_score: 'ERROR', intent_level: 'ERROR' })
              customerResults.increments.push(0)
            }
          }

          // 计算总增量
          const totalIncrement = customerResults.increments.reduce((sum, inc) => sum + inc, 0)

          // 构建CSV行
          const csvRow = [
            chatId,
            `"${chatName}"`,
            customerResults.preCourse.intent_score,
            `"${customerResults.preCourse.intent_level}"`,
            customerResults.increments[0] || 0,
            customerResults.days[0]?.intent_score || 'ERROR',
            `"${customerResults.days[0]?.intent_level || 'ERROR'}"`,
            customerResults.increments[1] || 0,
            customerResults.days[1]?.intent_score || 'ERROR',
            `"${customerResults.days[1]?.intent_level || 'ERROR'}"`,
            customerResults.increments[2] || 0,
            customerResults.days[2]?.intent_score || 'ERROR',
            `"${customerResults.days[2]?.intent_level || 'ERROR'}"`,
            customerResults.increments[3] || 0,
            customerResults.days[3]?.intent_score || 'ERROR',
            `"${customerResults.days[3]?.intent_level || 'ERROR'}"`,
            totalIncrement,
            customerResults.hasPurchased ? 'YES' : 'NO',
            'SUCCESS'
          ]

          return {
            index: globalIndex,
            name: chatName,
            csvRow: csvRow.join(','),
            preCourse: customerResults.preCourse.intent_score,
            totalIncrement
          }

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error)

          // 返回错误行
          const errorRow = [
            chatId,
            `"${chatName}"`,
            'ERROR', 'ERROR', 'ERROR', 'ERROR', 'ERROR',
            'ERROR', 'ERROR', 'ERROR', 'ERROR', 'ERROR', 'ERROR',
            'ERROR', 'ERROR', 'ERROR', 'ERROR', 'ERROR',
            `"${errorMessage}"`
          ]

          return {
            index: globalIndex,
            name: chatName,
            csvRow: errorRow.join(','),
            preCourse: 'ERROR',
            totalIncrement: 'ERROR',
            error: errorMessage
          }
        }
      })

      // 等待当前批次完成
      const batchResults = await Promise.all(batchPromises)

      // 处理结果
      const successCount = batchResults.filter((r) => !r.error).length
      const errorCount = batchResults.filter((r) => r.error).length

      batchResults.forEach((result) => {
        results.push(result.csvRow)
        if (result.error) {
          console.log(`   ❌ [${result.index}] ${result.name}: ${result.error}`)
        } else {
          console.log(`   ✅ [${result.index}] ${result.name}: ${result.preCourse}→${result.totalIncrement}`)
        }
      })

      const batchTime = ((Date.now() - batchStartTime) / 1000).toFixed(1)
      console.log(`   📊 批次完成: 成功${successCount}, 失败${errorCount}, 耗时${batchTime}秒`)
      console.log(`   ⚡ 平均速度: ${(batch.length / parseFloat(batchTime)).toFixed(1)} 客户/秒\n`)
    }

    // 组装最终CSV内容并写入文件
    csvContent += `${results.join('\n')  }\n`
    fs.writeFileSync(csvFilePath, csvContent, 'utf8')

    // 统计结果
    const totalSuccess = results.filter((row) => !row.includes('ERROR')).length
    const totalError = results.length - totalSuccess
    const fileSize = (fs.statSync(csvFilePath).size / 1024).toFixed(2)

    console.log('🎉 CSV报告生成完成!')
    console.log(`📁 文件路径: ${csvFilePath}`)
    console.log('📊 处理统计:')
    console.log(`   - 总客户数: ${chats.length}`)
    console.log(`   - 成功处理: ${totalSuccess}`)
    console.log(`   - 处理失败: ${totalError}`)
    console.log(`   - 成功率: ${((totalSuccess / chats.length) * 100).toFixed(1)}%`)
    console.log(`📋 文件大小: ${fileSize} KB`)
    console.log(`⏰ 完成时间: ${new Date().toLocaleString()}`)

    // 显示前几行预览
    console.log('\n📋 CSV内容预览:')
    const lines = csvContent.split('\n').slice(0, 4)
    lines.forEach((line, index) => {
      if (line.trim()) {
        const displayLine = line.length > 120 ? `${line.substring(0, 120)  }...` : line
        console.log(`${index === 0 ? '表头' : `客户${index}`}: ${displayLine}`)
      }
    })

    console.log('\n🎯 使用建议:')
    console.log('- 可以用Excel打开CSV文件进行数据分析')
    console.log(`- 如需处理更多客户，可调整参数: ts-node script/generate_intent_csv_parallel.ts ${courseNo} 500 50`)
    console.log(`- 如遇到内存问题，可降低并发数: ts-node script/generate_intent_csv_parallel.ts ${courseNo} 200 25`)

  } catch (error) {
    console.error('❌ 生成CSV报告失败:', error)
    process.exit(1)
  }
}

// 运行脚本
if (require.main === module) {
  console.log('🚀 并行累积意向度CSV生成器')
  console.log('使用方法: ts-node script/generate_intent_csv_parallel.ts [期数] [客户数量] [并发数]')
  console.log('例如: ts-node script/generate_intent_csv_parallel.ts 78 100 50')
  console.log('处理1000个客户: ts-node script/generate_intent_csv_parallel.ts 78 1000 50\n')

  generateIntentCSVParallel().catch(console.error)
}

export { generateIntentCSVParallel }
