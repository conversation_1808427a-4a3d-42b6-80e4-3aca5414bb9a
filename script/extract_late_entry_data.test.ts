import { PrismaMongoClient } from '../bot/model/mongodb/prisma'
import { DataService } from '../bot/service/moer/getter/getData'
import { PolyvAPI } from '../bot/model/polyv/polyv'
import { DateHelper } from '../bot/lib/date/date'
import { ChatStatStoreManager } from '../bot/service/moer/storage/chat_state_store'
import * as fs from 'fs'

interface LateEntryData {
  course_no: number | null
  customer_nickname: string
  moer_id: string | null
  has_ordered: boolean
  order_time: string | null // 下单时间
  order_weekday: string | null // 下单星期几
  order_time_formatted: string | null // 格式化的下单时间（周几+具体时间）
  order_week_type: string | null // 下单周类型：本周/下周

  // Day1数据
  day1_playback_time: number | null // 播放时长（秒）
  day1_start_time: number | null // 开始观看时间戳
  day1_late_entry: boolean // 是否20分钟后进入
  day1_entry_minutes: number | null // 进入时间（分钟）

  // Day2数据
  day2_playback_time: number | null
  day2_start_time: number | null
  day2_late_entry: boolean
  day2_entry_minutes: number | null

  // Day3数据
  day3_playback_time: number | null
  day3_start_time: number | null
  day3_late_entry: boolean
  day3_entry_minutes: number | null

  // Day4数据
  day4_playback_time: number | null
  day4_start_time: number | null
  day4_late_entry: boolean
  day4_entry_minutes: number | null
}

export class LateEntryAnalyzer {
  private static prisma = PrismaMongoClient.getInstance()

  /**
   * 分析69-71期的中途进入数据
   */
  public static async analyzeLateEntryData(
    outputPath: string = './late_entry_data_69_71.csv',
    concurrency: number = 20
  ): Promise<void> {
    console.log('开始分析69期到71期的中途进入数据...')

    const allData: LateEntryData[] = []

    // 获取69-71期的所有客户
    const customers = await this.prisma.chat.findMany({
      where: {
        course_no: {
          gte: 69,
          lte: 71
        }
      },
      select: {
        id: true,
        contact: true,
        chat_state: true,
        course_no: true,
        moer_id: true,
        created_at: true
      }
    })

    // 过滤掉内部测试号
    const filteredCustomers = customers.filter((customer) =>
      !['Dremo', '班级群任务', '韵如🦁'].includes(customer.contact.wx_name)
    )

    console.log(`找到 ${customers.length} 个客户，过滤后剩余 ${filteredCustomers.length} 个客户`)

    // 并发处理客户数据
    const results = await this.processCustomersConcurrently(filteredCustomers, concurrency)

    // 收集所有成功的数据
    results.forEach((result, index) => {
      if (result.success && result.data) {
        allData.push(result.data)
      } else {
        console.error(`处理客户 ${filteredCustomers[index].contact.wx_name} 时出错:`, result.error)
        // 添加空数据，保持数据完整性
        allData.push({
          course_no: filteredCustomers[index].course_no,
          customer_nickname: filteredCustomers[index].contact.wx_name,
          moer_id: filteredCustomers[index].moer_id,
          has_ordered: false,
          order_time: null,
          order_weekday: null,
          order_time_formatted: null,
          order_week_type: null,
          day1_playback_time: null,
          day1_start_time: null,
          day1_late_entry: false,
          day1_entry_minutes: null,
          day2_playback_time: null,
          day2_start_time: null,
          day2_late_entry: false,
          day2_entry_minutes: null,
          day3_playback_time: null,
          day3_start_time: null,
          day3_late_entry: false,
          day3_entry_minutes: null,
          day4_playback_time: null,
          day4_start_time: null,
          day4_late_entry: false,
          day4_entry_minutes: null
        })
      }
    })

    // 生成CSV文件
    await this.generateCSV(allData, outputPath)
    console.log(`数据已导出到: ${outputPath}`)
  }

  /**
   * 并发处理客户数据
   */
  private static async processCustomersConcurrently(
    customers: any[],
    concurrency: number
  ): Promise<Array<{ success: boolean; data?: LateEntryData; error?: any }>> {
    const results: Array<{ success: boolean; data?: LateEntryData; error?: any }> = []
    let processedCount = 0

    // 分批处理，控制并发数量
    for (let i = 0; i < customers.length; i += concurrency) {
      const batch = customers.slice(i, i + concurrency)
      const batchPromises = batch.map(async (customer, batchIndex) => {
        const globalIndex = i + batchIndex
        const customerName = customer.contact.wx_name

        try {
          console.log(`处理客户 ${globalIndex + 1}/${customers.length}: ${customerName}`)
          const customerData = await this.getCustomerLateEntryData(customer)
          processedCount++
          console.log(`✅ 完成客户 ${globalIndex + 1}/${customers.length}: ${customerName}`)
          return { success: true, data: customerData }
        } catch (error) {
          processedCount++
          console.error(`❌ 处理客户 ${globalIndex + 1}/${customers.length}: ${customerName} 时出错:`, error)
          return { success: false, error }
        }
      })

      // 等待当前批次完成
      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)

      // 显示进度
      const progress = ((processedCount / customers.length) * 100).toFixed(1)
      console.log(`📊 进度: ${processedCount}/${customers.length} (${progress}%)`)
    }

    return results
  }

  /**
   * 获取指定日期的星期几
   */
  private static getWeekDayString(date: Date): string {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return weekdays[date.getDay()]
  }

  /**
   * 获取下单时间
   */
  private static async getOrderTime(chatId: string, customer: any): Promise<string | null> {
    try {
      // 查找支付完成的事件记录
      const paymentEvent = await this.prisma.event_track.findFirst({
        where: {
          chat_id: chatId,
          type: '完成支付'
        },
        orderBy: {
          timestamp: 'desc'
        }
      })

      if (paymentEvent) {
        console.log(`    ✅ 找到完成支付事件: ${paymentEvent.timestamp.toISOString()}`)
        return paymentEvent.timestamp.toISOString()
      }

      console.log('    ❌ 未找到完成支付事件')
      return null
    } catch (error) {
      console.error('获取下单时间失败:', error)
      return null
    }
  }

  /**
   * 获取单个客户的中途进入数据
   */
  private static async getCustomerLateEntryData(customer: any): Promise<LateEntryData> {
    const chatId = customer.id
    const moerId = customer.moer_id

    console.log(`  📊 开始处理客户: ${customer.contact.wx_name} (chatId: ${chatId}, moerId: ${moerId})`)

    // 获取下单信息
    const hasOrdered = customer.chat_state?.state?.is_complete_payment || false

    // 只有已下单的客户才获取下单时间
    let orderTime: string | null = null
    let orderWeekday: string | null = null
    let orderTimeFormatted: string | null = null
    let orderWeekType: string | null = null

    if (hasOrdered) {
      orderTime = await this.getOrderTime(chatId, customer)
      if (orderTime) {
        const orderDate = new Date(orderTime)
        orderWeekday = this.getWeekDayString(orderDate)
        orderTimeFormatted = `${orderWeekday} ${DateHelper.formatDate(orderDate, 'MM-DD HH:mm')}`

        // 判断是本周还是下周下单
        const courseStartTime = await DataService.getCourseStartTimeByCourseNo(customer.course_no)
        if (courseStartTime) {
          const courseStartDate = new Date(courseStartTime)
          const orderDate = new Date(orderTime)

          // 计算下单时间与课程开始时间的差值（天数）
          const timeDiff = orderDate.getTime() - courseStartDate.getTime()
          const dayDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24))

          // 本周：课程开始的那一周（周一到周日，dayDiff >= 0 && dayDiff <= 6）
          // 下周：课程周之后的时间（dayDiff > 6）
          if (dayDiff >= 0 && dayDiff < 6) {
            orderWeekType = '本周'
          } else if (dayDiff >= 6) {
            orderWeekType = '下周'
          } else {
            // 如果下单时间在课程开始前，也标记为本周
            orderWeekType = '本周'
          }

          console.log(`    📅 下单时间: ${orderDate.toLocaleDateString()}, 课程开始: ${courseStartDate.toLocaleDateString()}, 差值: ${dayDiff}天, 类型: ${orderWeekType}`)
        } else {
          orderWeekType = '未知'
        }
      }
    }

    // 获取各天的中途进入数据
    const day1Data = await this.getDayLateEntryData(moerId, customer.course_no, 1)
    const day2Data = await this.getDayLateEntryData(moerId, customer.course_no, 2)
    const day3Data = await this.getDayLateEntryData(moerId, customer.course_no, 3)
    const day4Data = await this.getDayLateEntryData(moerId, customer.course_no, 4)

    return {
      course_no: customer.course_no,
      customer_nickname: customer.contact.wx_name,
      moer_id: customer.moer_id,
      has_ordered: hasOrdered,
      order_time: orderTime,
      order_weekday: orderWeekday,
      order_time_formatted: orderTimeFormatted,
      order_week_type: orderWeekType,
      day1_playback_time: day1Data['day1_playback_time'],
      day1_start_time: day1Data['day1_start_time'],
      day1_late_entry: day1Data['day1_late_entry'],
      day1_entry_minutes: day1Data['day1_entry_minutes'],
      day2_playback_time: day2Data['day2_playback_time'],
      day2_start_time: day2Data['day2_start_time'],
      day2_late_entry: day2Data['day2_late_entry'],
      day2_entry_minutes: day2Data['day2_entry_minutes'],
      day3_playback_time: day3Data['day3_playback_time'],
      day3_start_time: day3Data['day3_start_time'],
      day3_late_entry: day3Data['day3_late_entry'],
      day3_entry_minutes: day3Data['day3_entry_minutes'],
      day4_playback_time: day4Data['day4_playback_time'],
      day4_start_time: day4Data['day4_start_time'],
      day4_late_entry: day4Data['day4_late_entry'],
      day4_entry_minutes: day4Data['day4_entry_minutes']
    }
  }

  /**
   * 获取指定天的中途进入数据
   */
  private static async getDayLateEntryData(moerId: string | null, courseNo: number, day: number): Promise<Record<string, any>> {
    if (!moerId) {
      return {
        [`day${day}_playback_time`]: null,
        [`day${day}_start_time`]: null,
        [`day${day}_late_entry`]: false,
        [`day${day}_entry_minutes`]: null
      }
    }

    try {
      // 获取课程开始时间
      const courseStartTime = await DataService.getCourseStartTimeByCourseNo(courseNo)
      if (!courseStartTime) {
        return {
          [`day${day}_playback_time`]: null,
          [`day${day}_start_time`]: null,
          [`day${day}_late_entry`]: false,
          [`day${day}_entry_minutes`]: null
        }
      }

      // 计算指定天的日期
      const dayDate = new Date(courseStartTime)
      dayDate.setDate(dayDate.getDate() + day - 1)
      const formattedDate = DateHelper.formatDate(dayDate, 'YYYY-MM-DD')

      // 获取观看日志
      const viewLogData = await this.getViewLogData(moerId, formattedDate, day)

      if (!viewLogData) {
        return {
          [`day${day}_playback_time`]: null,
          [`day${day}_start_time`]: null,
          [`day${day}_late_entry`]: false,
          [`day${day}_entry_minutes`]: null
        }
      }

      // 计算进入时间（相对于课程开始时间）
      const courseStartTimestamp = new Date(dayDate).setHours(20, 0, 0, 0) // 假设课程在20:00开始
      const entryMinutes = Math.floor((viewLogData.startTime - courseStartTimestamp) / (1000 * 60))
      const isLateEntry = entryMinutes > 20 // 20分钟后进入

      console.log(`    Day${day}: 播放时长=${viewLogData.playbackTime}秒, 进入时间=${entryMinutes}分钟, 中途进入=${isLateEntry}`)

      return {
        [`day${day}_playback_time`]: viewLogData.playbackTime,
        [`day${day}_start_time`]: viewLogData.startTime,
        [`day${day}_late_entry`]: isLateEntry,
        [`day${day}_entry_minutes`]: entryMinutes
      }
    } catch (error) {
      console.error(`获取Day${day}中途进入数据失败:`, error)
      return {
        [`day${day}_playback_time`]: null,
        [`day${day}_start_time`]: null,
        [`day${day}_late_entry`]: false,
        [`day${day}_entry_minutes`]: null
      }
    }
  }

  /**
   * 获取观看日志数据
   */
  private static async getViewLogData(moerId: string, date: string, day: number): Promise<{
    playbackTime: number
    startTime: number
  } | null> {
    try {
      // 使用PolyvAPI获取观看日志
      const viewLogResponse = await PolyvAPI.getViewLog(moerId, date, date)

      if (!viewLogResponse.data || !viewLogResponse.data.contents) {
        return null
      }

      // 找到对应天的观看记录
      const dayContent = viewLogResponse.data.contents.find((content) => {
        // 这里需要根据实际的channelId或其他标识来匹配对应的课程
        // 暂时返回第一个记录
        return true
      })

      if (!dayContent) {
        return null
      }

      // 解析播放时长
      const playbackTime = this.parsePlayDuration(dayContent.playDuration)

      return {
        playbackTime,
        startTime: dayContent.startTime
      }
    } catch (error) {
      console.error(`获取观看日志失败 (moerId: ${moerId}, date: ${date}):`, error)
      return null
    }
  }

  /**
   * 解析播放时长字符串
   */
  private static parsePlayDuration(playDuration: string): number {
    const timeParts = playDuration.split(':')
    let seconds = 0

    if (timeParts.length === 3) {
      // 格式为 HH:MM:SS
      const hours = parseInt(timeParts[0], 10)
      const minutes = parseInt(timeParts[1], 10)
      const secs = parseInt(timeParts[2], 10)
      seconds = hours * 3600 + minutes * 60 + secs
    } else if (timeParts.length === 2) {
      // 格式为 MM:SS
      const minutes = parseInt(timeParts[0], 10)
      const secs = parseInt(timeParts[1], 10)
      seconds = minutes * 60 + secs
    } else if (timeParts.length === 1) {
      // 格式为 SS
      seconds = parseInt(timeParts[0], 10)
    }

    return seconds
  }

  /**
   * 生成CSV文件
   */
  private static async generateCSV(data: LateEntryData[], outputPath: string): Promise<void> {
    if (data.length === 0) {
      console.log('没有数据可导出')
      return
    }

    // 定义CSV头部
    const headers = [
      '课程编号',
      '客户昵称',
      '客户是否下单',
      '下单时间',
      '下单星期几',
      '格式化下单时间',
      '下单周类型',
      'day1播放时长(秒)',
      'day1开始时间',
      'day1进入时间(分钟)',
      'day1是否中途进入',
      'day2播放时长(秒)',
      'day2开始时间',
      'day2进入时间(分钟)',
      'day2是否中途进入',
      'day3播放时长(秒)',
      'day3开始时间',
      'day3进入时间(分钟)',
      'day3是否中途进入',
      'day4播放时长(秒)',
      'day4开始时间',
      'day4进入时间(分钟)',
      'day4是否中途进入'
    ]

    // 生成CSV内容
    const csvRows = [headers.join(',')]

    for (const row of data) {
      const csvRow = [
        row.course_no || '',
        `"${row.customer_nickname}"`,
        row.has_ordered ? '是' : '否',
        row.order_time || '',
        row.order_weekday || '',
        row.order_time_formatted || '',
        row.order_week_type || '',
        row.day1_playback_time || '',
        row.day1_start_time || '',
        row.day1_entry_minutes || '',
        row.day1_late_entry ? '是' : '否',
        row.day2_playback_time || '',
        row.day2_start_time || '',
        row.day2_entry_minutes || '',
        row.day2_late_entry ? '是' : '否',
        row.day3_playback_time || '',
        row.day3_start_time || '',
        row.day3_entry_minutes || '',
        row.day3_late_entry ? '是' : '否',
        row.day4_playback_time || '',
        row.day4_start_time || '',
        row.day4_entry_minutes || '',
        row.day4_late_entry ? '是' : '否'
      ]
      csvRows.push(csvRow.join(','))
    }

    // 写入文件
    const csvContent = csvRows.join('\n')
    await fs.promises.writeFile(outputPath, csvContent, 'utf8')
  }
}

it('latesp', async () => {
  await LateEntryAnalyzer.analyzeLateEntryData('./late_entry_data_69_71.csv', 30)
}, 600000)

