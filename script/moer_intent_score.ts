import { PrismaMongoClient } from '../bot/model/mongodb/prisma'
import { DataService } from '../bot/service/moer/getter/getData'
import { MoerAPI } from '../bot/model/moer_api/moer'
// import { ScrmAPI } from '../bot/model/scrm_api/scrm' // 注释掉新接口，将来使用
import { PolyvAPI } from '../bot/model/polyv/polyv' // 使用旧的Polyv接口
import { DateHelper } from '../bot/lib/date/date'
import * as fs from 'fs'
import * as path from 'path'

// 定义客户意向度数据接口
interface CustomerIntentData {
  // 基本信息
  course_no: number | null
  customer_nickname: string
  moer_id: string | null

  // 接量期数据
  pre_course_intent_score: number // 接量期意向度分数
  pre_course_leading_course: boolean // 先导课完成
  pre_course_conversation_rounds: number // 对话轮次
  pre_course_energy_test: boolean // 能量测评完成
  pre_course_need_mining: boolean // 挖需完成（对话轮次>15）

  // Day1数据
  day1_intent_score: number // Day1意向度分数
  day1_course_participation: number // 课程参与度（完课比）
  day1_live_comments: number // 直播间发言次数
  day1_live_comments_percentile: number // 直播间发言次数百分位
  day1_after_course_feeling: boolean // 课后感悟
  day1_conversation_rounds: number // 对话轮次

  // Day2数据
  day2_intent_score: number // Day2意向度分数
  day2_course_participation: number // 课程参与度
  day2_wealth_orchard_homework: boolean // 财富果园作业
  day2_conversation_rounds: number // 对话轮次

  // Day3数据
  day3_intent_score: number // Day3意向度分数
  day3_course_participation: number // 课程参与度
  day3_conversation_rounds: number // 对话轮次

  // 综合意向度
  overall_intent_score: number // 综合意向度分数
}

// 定义课程信息接口
interface CourseInfo {
  course_no: number
  start_date: string
  day1_date: string
  day2_date: string
  day3_date: string
  day1_live_id?: number
  day2_live_id?: number
  day3_live_id?: number
}

/**
 * 墨尔意向度计算服务
 */
export class MoerIntentScoreService {
  private static prisma = PrismaMongoClient.getInstance()

  /**
   * 计算指定课程编号范围内所有客户的意向度
   * @param startCourseNo 开始课程编号
   * @param endCourseNo 结束课程编号
   * @param outputPath 输出文件路径
   * @param concurrency 并发数量，默认10
   */
  public static async calculateIntentScore(
    startCourseNo?: number,
    endCourseNo?: number,
    outputPath: string = './intent_score_data.csv',
    concurrency: number = 10
  ): Promise<void> {
    console.log(`开始计算课程编号 ${startCourseNo || '全部'} 到 ${endCourseNo || '全部'} 的客户意向度...`)

    // 构建查询条件
    const whereCondition: any = {}
    if (startCourseNo && endCourseNo) {
      whereCondition.course_no = {
        gte: startCourseNo,
        lte: endCourseNo
      }
    } else if (startCourseNo) {
      whereCondition.course_no = {
        gte: startCourseNo
      }
    } else if (endCourseNo) {
      whereCondition.course_no = {
        lte: endCourseNo
      }
    }

    // 获取指定课程编号范围内的所有客户
    const customers = await this.prisma.chat.findMany({
      where: whereCondition,
      select: {
        id: true,
        contact: true,
        course_no: true,
        moer_id: true,
        created_at: true
      }
    })

    // 过滤掉内部测试号
    const filteredCustomers = customers.filter(customer =>
      !['Dremo', '班级群任务', '韵如🦁'].includes(customer.contact.wx_name)
    )

    console.log(`找到 ${customers.length} 个客户，过滤后剩余 ${filteredCustomers.length} 个客户`)
    console.log(`使用并发数量: ${concurrency}`)

    // 并发处理客户数据
    const results = await this.processCustomersConcurrently(filteredCustomers, concurrency)

    // 收集所有成功的数据
    const allData: CustomerIntentData[] = []
    results.forEach((result, index) => {
      if (result.success && result.data) {
        allData.push(result.data)
      } else {
        console.error(`处理客户 ${filteredCustomers[index].contact.wx_name} 时出错:`, result.error)
        // 添加空数据，保持数据完整性
        allData.push({
          course_no: filteredCustomers[index].course_no,
          customer_nickname: filteredCustomers[index].contact.wx_name,
          moer_id: filteredCustomers[index].moer_id,
          pre_course_intent_score: 0,
          pre_course_leading_course: false,
          pre_course_conversation_rounds: 0,
          pre_course_energy_test: false,
          pre_course_need_mining: false,
          day1_intent_score: 0,
          day1_course_participation: 0,
          day1_live_comments: 0,
          day1_live_comments_percentile: 0,
          day1_after_course_feeling: false,
          day1_conversation_rounds: 0,
          day2_intent_score: 0,
          day2_course_participation: 0,
          day2_wealth_orchard_homework: false,
          day2_conversation_rounds: 0,
          day3_intent_score: 0,
          day3_course_participation: 0,
          day3_conversation_rounds: 0,
          overall_intent_score: 0
        })
      }
    })

    // 生成CSV文件
    await this.generateCSV(allData, outputPath)
    console.log(`意向度数据已导出到: ${outputPath}`)
  }

  /**
   * 并发处理客户数据
   */
  private static async processCustomersConcurrently(
    customers: any[],
    concurrency: number
  ): Promise<Array<{ success: boolean; data?: CustomerIntentData; error?: string }>> {
    const results: Array<{ success: boolean; data?: CustomerIntentData; error?: string }> = []
    
    for (let i = 0; i < customers.length; i += concurrency) {
      const batch = customers.slice(i, i + concurrency)
      const batchPromises = batch.map(async (customer) => {
        try {
          const data = await this.getCustomerIntentData(customer)
          return { success: true, data }
        } catch (error) {
          return { success: false, error: error instanceof Error ? error.message : String(error) }
        }
      })
      
      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)
      
      console.log(`已处理 ${Math.min(i + concurrency, customers.length)} / ${customers.length} 个客户`)
    }
    
    return results
  }

  /**
   * 获取单个客户的意向度数据
   */
  private static async getCustomerIntentData(customer: any): Promise<CustomerIntentData> {
    const chatId = customer.id
    const courseNo = customer.course_no

    if (!courseNo) {
      throw new Error('课程编号为空')
    }

    // 获取课程信息
    const courseInfo = await this.getCourseInfo(courseNo)
    if (!courseInfo) {
      throw new Error(`无法获取课程 ${courseNo} 的信息`)
    }

    // 获取接量期数据
    const preCourseData = await this.getPreCourseData(chatId, courseInfo)

    // 获取Day1数据
    const day1Data = await this.getDay1Data(chatId, courseInfo)

    // 获取Day2数据
    const day2Data = await this.getDay2Data(chatId, courseInfo)

    // 获取Day3数据
    const day3Data = await this.getDay3Data(chatId, courseInfo)

    // 计算综合意向度
    const overallIntentScore = this.calculateOverallIntentScore(preCourseData, day1Data, day2Data, day3Data)

    return {
      course_no: customer.course_no,
      customer_nickname: customer.contact.wx_name,
      moer_id: customer.moer_id,
      ...preCourseData,
      ...day1Data,
      ...day2Data,
      ...day3Data,
      overall_intent_score: overallIntentScore
    }
  }

  /**
   * 获取课程信息
   */
  private static async getCourseInfo(courseNo: number): Promise<CourseInfo | null> {
    try {
      const courseInfo = await MoerAPI.getCurrentCourseInfo(courseNo)
      if (!courseInfo || courseInfo.code !== 0) {
        return null
      }

      const startDate = new Date(courseInfo.data.startTime)
      const day1Date = DateHelper.formatDate(startDate, 'YYYY-MM-DD')
      const day2Date = DateHelper.formatDate(new Date(startDate.getTime() + 24 * 60 * 60 * 1000), 'YYYY-MM-DD')
      const day3Date = DateHelper.formatDate(new Date(startDate.getTime() + 2 * 24 * 60 * 60 * 1000), 'YYYY-MM-DD')

      // 获取直播ID
      const day1Live = courseInfo.data.resource.find(r => r.day === 1)
      const day2Live = courseInfo.data.resource.find(r => r.day === 2)
      const day3Live = courseInfo.data.resource.find(r => r.day === 3)

      return {
        course_no: courseNo,
        start_date: DateHelper.formatDate(startDate, 'YYYY-MM-DD'),
        day1_date: day1Date,
        day2_date: day2Date,
        day3_date: day3Date,
        day1_live_id: day1Live?.liveId,
        day2_live_id: day2Live?.liveId,
        day3_live_id: day3Live?.liveId
      }
    } catch (error) {
      console.error(`获取课程 ${courseNo} 信息失败:`, error)
      return null
    }
  }

  /**
   * 获取接量期数据
   */
  private static async getPreCourseData(chatId: string, courseInfo: CourseInfo) {
    // 获取对话轮次（接量期前）
    const conversationRounds = await this.getConversationRounds(chatId, courseInfo.start_date)
    
    // 判断挖需完成（对话轮次>15）
    const needMining = conversationRounds > 15

    // 获取先导课完成情况
    const leadingCourse = await this.isLeadingCourseCompleted(chatId, courseInfo)

    // 获取能量测评完成情况
    const energyTest = await this.isEnergyTestCompleted(chatId, courseInfo)

    // 计算接量期意向度分数
    const intentScore = this.calculatePreCourseIntentScore(leadingCourse, conversationRounds, energyTest, needMining)

    return {
      pre_course_intent_score: intentScore,
      pre_course_leading_course: leadingCourse,
      pre_course_conversation_rounds: conversationRounds,
      pre_course_energy_test: energyTest,
      pre_course_need_mining: needMining
    }
  }

  /**
   * 获取Day1数据
   */
  private static async getDay1Data(chatId: string, courseInfo: CourseInfo) {
    // 获取课程参与度
    const courseParticipation = await this.getCourseParticipation(chatId, courseInfo, 1)

    // 获取直播间发言次数
    const liveComments = await this.getLiveComments(chatId, courseInfo.day1_live_id, courseInfo.day1_date)

    // 获取课后感悟
    const afterCourseFeeling = await this.isAfterCourseFeelingCompleted(chatId, courseInfo, 1)

    // 获取对话轮次
    const conversationRounds = await this.getConversationRounds(chatId, courseInfo.day1_date, courseInfo.day2_date)

    // 计算直播间发言次数百分位（这里简化处理，实际需要所有用户数据）
    const liveCommentsPercentile = this.calculatePercentile(liveComments, 50) // 假设中位数

    // 计算Day1意向度分数
    const intentScore = this.calculateDay1IntentScore(courseParticipation, liveCommentsPercentile, afterCourseFeeling, conversationRounds)

    return {
      day1_intent_score: intentScore,
      day1_course_participation: courseParticipation,
      day1_live_comments: liveComments,
      day1_live_comments_percentile: liveCommentsPercentile,
      day1_after_course_feeling: afterCourseFeeling,
      day1_conversation_rounds: conversationRounds
    }
  }

  /**
   * 获取Day2数据
   */
  private static async getDay2Data(chatId: string, courseInfo: CourseInfo) {
    // 获取课程参与度
    const courseParticipation = await this.getCourseParticipation(chatId, courseInfo, 2)

    // 获取财富果园作业完成情况
    const wealthOrchardHomework = await this.isWealthOrchardHomeworkCompleted(chatId, courseInfo)

    // 获取对话轮次
    const conversationRounds = await this.getConversationRounds(chatId, courseInfo.day2_date, courseInfo.day3_date)

    // 计算Day2意向度分数
    const intentScore = this.calculateDay2IntentScore(courseParticipation, wealthOrchardHomework, conversationRounds)

    return {
      day2_intent_score: intentScore,
      day2_course_participation: courseParticipation,
      day2_wealth_orchard_homework: wealthOrchardHomework,
      day2_conversation_rounds: conversationRounds
    }
  }

  /**
   * 获取Day3数据
   */
  private static async getDay3Data(chatId: string, courseInfo: CourseInfo) {
    // 获取课程参与度
    const courseParticipation = await this.getCourseParticipation(chatId, courseInfo, 3)

    // 获取对话轮次
    const conversationRounds = await this.getConversationRounds(chatId, courseInfo.day3_date)

    // 计算Day3意向度分数
    const intentScore = this.calculateDay3IntentScore(courseParticipation, conversationRounds)

    return {
      day3_intent_score: intentScore,
      day3_course_participation: courseParticipation,
      day3_conversation_rounds: conversationRounds
    }
  }

  /**
   * 获取对话轮次
   */
  private static async getConversationRounds(chatId: string, startDate?: string, endDate?: string): Promise<number> {
    const whereCondition: any = { chat_id: chatId }
    
    if (startDate && endDate) {
      whereCondition.created_at = {
        gte: new Date(startDate),
        lt: new Date(endDate)
      }
    } else if (startDate) {
      whereCondition.created_at = {
        lt: new Date(startDate)
      }
    }

    const chatHistory = await this.prisma.chat_history.findMany({
      where: whereCondition,
      select: { role: true }
    })

    // 计算用户消息数量
    return chatHistory.filter(msg => msg.role === 'user').length
  }

  /**
   * 获取课程参与度（完课比）
   */
  private static async getCourseParticipation(chatId: string, courseInfo: CourseInfo, day: number): Promise<number> {
    try {
      const moerId = await this.getMoerId(chatId)
      if (!moerId) return 0

      // 获取课程资源信息
      const courseResource = await MoerAPI.getCurrentCourseInfo(courseInfo.course_no)
      if (!courseResource || courseResource.code !== 0) return 0

      const dayResource = courseResource.data.resource.find(r => r.day === day)
      if (!dayResource) return 0

      // 获取课程完成状态
      const courseStatus = await MoerAPI.getUserChapterStatus({
        userId: moerId,
        liveId: dayResource.liveId?.toString(),
        vodId: dayResource.vodId?.toString(),
        sku: courseResource.data.sku
      })

      if (!courseStatus || !courseStatus.duration) return 0

      const playbackTime = typeof courseStatus.playbackTime === 'string' 
        ? Number(courseStatus.playbackTime) 
        : courseStatus.playbackTime

      return Math.min(playbackTime / courseStatus.duration, 1)
    } catch (error) {
      console.error(`获取课程参与度失败:`, error)
      return 0
    }
  }

  /**
   * 获取直播间发言次数
   */
  private static async getLiveComments(chatId: string, liveId?: number, date?: string): Promise<number> {
    if (!liveId || !date) return 0

    try {
      const moerId = await this.getMoerId(chatId)
      if (!moerId) return 0

      // 使用旧的Polyv API获取直播间发言次数
      const danmuData = await PolyvAPI.getLiveDanmu(liveId.toString(), date, date)
      
      // 统计该用户在指定日期的发言次数
      const userComments = danmuData.filter(danmu => 
        danmu.user && danmu.user.userId === moerId
      )
      
      return userComments.length
    } catch (error) {
      console.error(`获取直播间发言次数失败:`, error)
      return 0
    }
  }

  /**
   * 获取直播间发言次数 - 新SCRM API版本（注释掉，将来使用）
   */
  /*
  private static async getLiveCommentsNew(chatId: string, liveId?: number, date?: string): Promise<number> {
    if (!liveId || !date) return 0

    try {
      const moerId = await this.getMoerId(chatId)
      if (!moerId) return 0

      // 使用SCRM API获取直播间发言次数
      return await ScrmAPI.getUserLiveCommentCountByDateRange(liveId, moerId, date, date)
    } catch (error) {
      console.error(`获取直播间发言次数失败:`, error)
      return 0
    }
  }
  */

  /**
   * 获取Moer ID
   */
  private static async getMoerId(chatId: string): Promise<string | null> {
    const chat = await this.prisma.chat.findUnique({
      where: { id: chatId },
      select: { moer_id: true }
    })
    return chat?.moer_id || null
  }

  /**
   * 判断先导课是否完成
   */
  private static async isLeadingCourseCompleted(chatId: string, courseInfo: CourseInfo): Promise<boolean> {
    try {
      const chat = await this.prisma.chat.findUnique({
        where: { id: chatId },
        select: { chat_state: true }
      })
      
      if (!chat || !chat.chat_state || !chat.chat_state.state) {
        return false
      }
      
      const state = chat.chat_state.state as any
      return Boolean(state.is_complete_pre_course)
    } catch (error) {
      console.error(`获取先导课完成状态失败:`, error)
      return false
    }
  }

  /**
   * 判断能量测评是否完成
   */
  private static async isEnergyTestCompleted(chatId: string, courseInfo: CourseInfo): Promise<boolean> {
    try {
      const chat = await this.prisma.chat.findUnique({
        where: { id: chatId },
        select: { chat_state: true }
      })
      
      if (!chat || !chat.chat_state || !chat.chat_state.state) {
        return false
      }
      
      const state = chat.chat_state.state as any
      // 检查能量测评完成状态，包括测试和分析
      return Boolean(state.is_complete_energy_test || state.is_complete_energy_test_analyze)
    } catch (error) {
      console.error(`获取能量测评完成状态失败:`, error)
      return false
    }
  }

  /**
   * 判断课后感悟是否完成
   */
  private static async isAfterCourseFeelingCompleted(chatId: string, courseInfo: CourseInfo, day: number): Promise<boolean> {
    try {
      const chat = await this.prisma.chat.findUnique({
        where: { id: chatId },
        select: { chat_state: true }
      })
      
      if (!chat || !chat.chat_state || !chat.chat_state.state) {
        return false
      }
      
      const state = chat.chat_state.state as any
      // 根据天数判断对应的作业完成状态
      switch (day) {
        case 1:
          // Day1课后感悟：检查作业完成和作业反馈完成
          return Boolean(state.is_complete_day1_homework || state.is_complete_day1_homework_feedback)
        case 2:
          return Boolean(state.is_complete_day2_homework || state.is_complete_day2_homework_feedback)
        case 3:
          return Boolean(state.is_complete_day3_homework || state.is_complete_day3_homework_feedback)
        case 4:
          return Boolean(state.is_complete_day4_homework || state.is_complete_day4_homework_feedback)
        default:
          return false
      }
    } catch (error) {
      console.error(`获取课后感悟完成状态失败:`, error)
      return false
    }
  }

  /**
   * 判断财富果园作业是否完成
   */
  private static async isWealthOrchardHomeworkCompleted(chatId: string, courseInfo: CourseInfo): Promise<boolean> {
    try {
      const chat = await this.prisma.chat.findUnique({
        where: { id: chatId },
        select: { chat_state: true }
      })
      
      if (!chat || !chat.chat_state || !chat.chat_state.state) {
        return false
      }
      
      const state = chat.chat_state.state as any
      // 检查Day2作业完成状态（财富果园作业）
      return Boolean(state.is_complete_day2_homework || state.is_complete_day2_homework_feedback)
    } catch (error) {
      console.error(`获取财富果园作业完成状态失败:`, error)
      return false
    }
  }

  /**
   * 计算接量期意向度分数
   */
  private static calculatePreCourseIntentScore(
    leadingCourse: boolean,
    conversationRounds: number,
    energyTest: boolean,
    needMining: boolean
  ): number {
    const leadingCourseScore = leadingCourse ? 25 : 0
    const conversationScore = Math.min(conversationRounds * 2, 25) // 每轮对话2分，最高25分
    const energyTestScore = energyTest ? 25 : 0
    const needMiningScore = needMining ? 25 : 0

    return leadingCourseScore + conversationScore + energyTestScore + needMiningScore
  }

  /**
   * 计算Day1意向度分数
   */
  private static calculateDay1IntentScore(
    courseParticipation: number,
    liveCommentsPercentile: number,
    afterCourseFeeling: boolean,
    conversationRounds: number
  ): number {
    const participationScore = courseParticipation * 0.8 * 50 + liveCommentsPercentile * 0.2 * 50
    const feelingScore = afterCourseFeeling ? 20 : 0
    const conversationScore = Math.min(conversationRounds * 3, 30) // 每轮对话3分，最高30分

    return participationScore + feelingScore + conversationScore
  }

  /**
   * 计算Day2意向度分数
   */
  private static calculateDay2IntentScore(
    courseParticipation: number,
    wealthOrchardHomework: boolean,
    conversationRounds: number
  ): number {
    const participationScore = courseParticipation * 60
    const homeworkScore = wealthOrchardHomework ? 30 : 0
    const conversationScore = Math.min(conversationRounds * 1, 10) // 每轮对话1分，最高10分

    return participationScore + homeworkScore + conversationScore
  }

  /**
   * 计算Day3意向度分数
   */
  private static calculateDay3IntentScore(
    courseParticipation: number,
    conversationRounds: number
  ): number {
    const participationScore = courseParticipation * 70
    const conversationScore = Math.min(conversationRounds * 3, 30) // 每轮对话3分，最高30分

    return participationScore + conversationScore
  }

  /**
   * 计算综合意向度分数
   */
  private static calculateOverallIntentScore(
    preCourseData: any,
    day1Data: any,
    day2Data: any,
    day3Data: any
  ): number {
    // 可以根据业务需求调整权重
    const weights = {
      preCourse: 0.2,
      day1: 0.3,
      day2: 0.3,
      day3: 0.2
    }

    return (
      preCourseData.pre_course_intent_score * weights.preCourse +
      day1Data.day1_intent_score * weights.day1 +
      day2Data.day2_intent_score * weights.day2 +
      day3Data.day3_intent_score * weights.day3
    )
  }

  /**
   * 计算百分位（简化版本）
   */
  private static calculatePercentile(value: number, percentile: number): number {
    // 这里简化处理，实际需要根据所有用户的数据来计算百分位
    return Math.min(value / 10, 100) // 假设10次发言为100分位
  }

  /**
   * 生成CSV文件
   */
  private static async generateCSV(data: CustomerIntentData[], outputPath: string): Promise<void> {
    const headers = [
      'course_no',
      'customer_nickname',
      'moer_id',
      'pre_course_intent_score',
      'pre_course_leading_course',
      'pre_course_conversation_rounds',
      'pre_course_energy_test',
      'pre_course_need_mining',
      'day1_intent_score',
      'day1_course_participation',
      'day1_live_comments',
      'day1_live_comments_percentile',
      'day1_after_course_feeling',
      'day1_conversation_rounds',
      'day2_intent_score',
      'day2_course_participation',
      'day2_wealth_orchard_homework',
      'day2_conversation_rounds',
      'day3_intent_score',
      'day3_course_participation',
      'day3_conversation_rounds',
      'overall_intent_score'
    ]

    const csvContent = [
      headers.join(','),
      ...data.map(row => [
        row.course_no,
        `"${row.customer_nickname}"`,
        row.moer_id,
        row.pre_course_intent_score,
        row.pre_course_leading_course,
        row.pre_course_conversation_rounds,
        row.pre_course_energy_test,
        row.pre_course_need_mining,
        row.day1_intent_score,
        row.day1_course_participation,
        row.day1_live_comments,
        row.day1_live_comments_percentile,
        row.day1_after_course_feeling,
        row.day1_conversation_rounds,
        row.day2_intent_score,
        row.day2_course_participation,
        row.day2_wealth_orchard_homework,
        row.day2_conversation_rounds,
        row.day3_intent_score,
        row.day3_course_participation,
        row.day3_conversation_rounds,
        row.overall_intent_score
      ].join(','))
    ].join('\n')

    await fs.promises.writeFile(outputPath, csvContent, 'utf8')
  }
} 