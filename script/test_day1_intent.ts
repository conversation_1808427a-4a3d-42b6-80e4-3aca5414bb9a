#!/usr/bin/env ts-node

import { NewIntentCalculator } from '../bot/service/moer/components/signals/new_intent_score'
import { DataService } from '../bot/service/moer/getter/getData'
import { ChatStatStoreManager } from '../bot/service/moer/storage/chat_state_store'
import { ChatHistoryService } from '../bot/service/moer/components/chat_history/chat_history'

/**
 * 测试第一天意向度计算的脚本
 * 用于验证新的第一天计算逻辑
 */

async function testDay1IntentCalculation() {
  try {
    console.log('🚀 开始测试第一天意向度计算...\n')

    // 获取测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('❌ 没有找到78期的客户')
      return
    }

    // 测试前3个客户
    const testChats = chats.slice(0, 3)
    
    for (const chat of testChats) {
      console.log(`\n📋 测试客户第一天: ${chat.contact.wx_name} (${chat.id})`)
      console.log('=' .repeat(70))
      
      // 初始化客户状态
      await ChatStatStoreManager.initState(chat.id)
      
      // 获取课程开始时间
      const courseStartTime = await DataService.getCourseStartTime(chat.id)
      console.log(`📅 课程开始时间: ${courseStartTime}`)
      
      // 计算第一天时间范围
      const day1StartTime = new Date(courseStartTime)
      const day1EndTime = new Date(courseStartTime)
      day1EndTime.setDate(day1EndTime.getDate() + 1)
      
      console.log(`📅 第一天时间范围: ${day1StartTime} 到 ${day1EndTime}`)
      
      // 获取聊天历史统计
      const allChatHistory = await ChatHistoryService.getChatHistoryByChatId(chat.id)
      const day1Messages = allChatHistory.filter(msg => {
        const messageTime = new Date(msg.created_at)
        return messageTime >= day1StartTime && messageTime < day1EndTime
      })
      const day1UserMessages = day1Messages.filter(msg => msg.role === 'user')
      
      console.log(`💬 第一天总消息: ${day1Messages.length} 条`)
      console.log(`👤 第一天用户消息: ${day1UserMessages.length} 条`)
      
      // 计算第一天意向度
      try {
        const day1Result = await NewIntentCalculator.calculateCourseIntentScore(chat.id, 1)
        
        console.log(`\n📊 第一天意向度结果:`)
        console.log(`   总分: ${day1Result.intent_score} 分`)
        console.log(`   等级: ${day1Result.intent_level}`)
        console.log(`   基础数据:`)
        console.log(`   - 对话轮数: ${day1Result.course_data?.conversation_rounds}`)
        console.log(`   - 观看分钟数: ${day1Result.course_data?.course_watch_minutes}`)
        console.log(`   - 有打卡: ${day1Result.course_data?.replied_after_course_feeling}`)
        
        // 获取详细数据
        const day1Data = await (NewIntentCalculator as any).getDay1Data(chat.id)
        
        console.log(`\n🔍 详细分析:`)
        console.log(`   - 总回复数: ${day1Data.totalReplies}`)
        console.log(`   - 从未回复: ${day1Data.neverReplied}`)
        console.log(`   - 开课前对话比例: ${day1Data.preCourseConversationRatio.toFixed(2)}`)
        console.log(`   - 回复参加但未参加: ${day1Data.repliedJoinButNotJoin}`)
        console.log(`   - 给出不参加原因: ${day1Data.gaveReasonForNotJoining}`)
        console.log(`   - 完成课程: ${day1Data.courseCompleted}`)
        console.log(`   - 打卡情感: ${day1Data.checkInSentiment}`)
        
        // 计算各项得分
        console.log(`\n📈 得分明细:`)
        
        // 1. 开课前对话比例
        if (day1Data.preCourseConversationRatio > 1) {
          console.log(`   ✅ 开课前对话比例高: +10分`)
        } else {
          console.log(`   ❌ 开课前对话比例低: 0分`)
        }
        
        // 2. 当天完全没回复
        if (day1Data.totalReplies === 0) {
          console.log(`   ❌ 当天完全没回复: -10分`)
        } else {
          console.log(`   ✅ 当天有回复: 0分`)
        }
        
        // 3. 从未对话过
        if (day1Data.neverReplied) {
          console.log(`   ❌ 从未对话过: -10分`)
        } else {
          console.log(`   ✅ 有过对话: 0分`)
        }
        
        // 4. 回复参加但未参加
        if (day1Data.repliedJoinButNotJoin) {
          console.log(`   ❌ 回复参加但未参加: -5分`)
        } else {
          console.log(`   ✅ 没有虚假承诺: 0分`)
        }
        
        // 5. 给出不参加原因
        if (day1Data.gaveReasonForNotJoining) {
          console.log(`   ✅ 给出不参加原因: +5分`)
        } else {
          console.log(`   ❌ 未给出不参加原因: 0分`)
        }
        
        // 6. 看课进度
        if (day1Data.courseWatchMinutes >= 10) {
          console.log(`   ✅ 到课(≥10分钟): +10分`)
        } else {
          console.log(`   ❌ 未到课(<10分钟): 0分`)
        }
        
        if (day1Data.courseCompleted) {
          console.log(`   ✅ 完课: +10分`)
        } else {
          console.log(`   ❌ 未完课: 0分`)
        }
        
        // 7. 打卡
        if (day1Data.hasCheckedIn) {
          console.log(`   ✅ 有打卡: +10分`)
        } else {
          console.log(`   ❌ 无打卡: 0分`)
        }
        
        // 8. 打卡情感分数
        const sentimentScore = (NewIntentCalculator as any).calculateCheckInScore(day1Data.checkInSentiment)
        console.log(`   📝 打卡情感(${day1Data.checkInSentiment}): ${sentimentScore > 0 ? '+' : ''}${sentimentScore}分`)
        
      } catch (error) {
        console.error(`❌ 计算第一天意向度失败:`, error)
      }
      
      console.log('\n' + '─'.repeat(70))
    }
    
    console.log('\n✅ 第一天意向度测试完成!')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
  }
}

// 运行测试
if (require.main === module) {
  testDay1IntentCalculation().catch(console.error)
}

export { testDay1IntentCalculation }
