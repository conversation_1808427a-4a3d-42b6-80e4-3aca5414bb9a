#!/usr/bin/env ts-node

import { NewIntentCalculator } from '../bot/service/moer/components/signals/new_intent_score'
import { DataService } from '../bot/service/moer/getter/getData'
import { ChatStatStoreManager } from '../bot/service/moer/storage/chat_state_store'
import { ChatHistoryService } from '../bot/service/moer/components/chat_history/chat_history'

/**
 * 测试接量期意向度计算的脚本
 * 用于验证新的计算逻辑是否正确筛选接量期数据
 */

async function testPreCourseIntentCalculation() {
  try {
    console.log('🚀 开始测试接量期意向度计算...\n')

    // 获取测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('❌ 没有找到78期的客户')
      return
    }

    // 测试前3个客户
    const testChats = chats.slice(0, 3)
    
    for (const chat of testChats) {
      console.log(`\n📋 测试客户: ${chat.contact.wx_name} (${chat.id})`)
      console.log('=' .repeat(60))
      
      // 初始化客户状态
      await ChatStatStoreManager.initState(chat.id)
      
      // 获取课程开始时间
      const courseStartTime = await DataService.getCourseStartTime(chat.id)
      console.log(`📅 课程开始时间: ${courseStartTime}`)
      
      // 获取聊天历史统计
      const allChatHistory = await ChatHistoryService.getChatHistoryByChatId(chat.id)
      const preCourseHistory = allChatHistory.filter((msg) => {
        const messageTime = new Date(msg.created_at)
        return messageTime < courseStartTime
      })
      const preCourseUserMessages = preCourseHistory.filter(msg => msg.role === 'user')
      
      console.log(`💬 总聊天记录: ${allChatHistory.length} 条`)
      console.log(`📝 接量期聊天记录: ${preCourseHistory.length} 条`)
      console.log(`👤 接量期用户消息: ${preCourseUserMessages.length} 条`)
      
      // 计算接量期意向度
      const preCourseResult = await NewIntentCalculator.calculatePreCourseIntentScore(chat.id)
      
      console.log(`\n📊 接量期意向度结果:`)
      console.log(`   总分: ${preCourseResult.intent_score} 分`)
      console.log(`   等级: ${preCourseResult.intent_level}`)
      console.log(`   详细数据:`)
      console.log(`   - 有足够对话(>5轮): ${preCourseResult.pre_course_data?.has_conversation}`)
      console.log(`   - 完成先导课: ${preCourseResult.pre_course_data?.completed_leading_course}`)
      console.log(`   - 完成挖需: ${preCourseResult.pre_course_data?.completed_need_mining}`)
      console.log(`   - 完成能量测评: ${preCourseResult.pre_course_data?.completed_energy_test}`)
      console.log(`   - 接量期对话轮数: ${preCourseResult.pre_course_data?.conversation_rounds}`)
      
      // 测试各个子功能
      console.log(`\n🔍 详细检查:`)
      
      // 测试困扰表达检查
      try {
        const hasExpressedTroubles = await (NewIntentCalculator as any).checkHasExpressedTroubles(chat.id)
        console.log(`   - 表达过困扰: ${hasExpressedTroubles}`)
      } catch (error) {
        console.log(`   - 表达过困扰: 检查失败 (${error.message})`)
      }
      
      // 测试先导课分数
      try {
        const leadingCourseScore = await (NewIntentCalculator as any).calculateLeadingCourseScore(chat.id)
        console.log(`   - 先导课分数: ${leadingCourseScore}`)
      } catch (error) {
        console.log(`   - 先导课分数: 检查失败 (${error.message})`)
      }
      
      // 测试意向调研回复
      try {
        const repliedIntentionQuery = await (NewIntentCalculator as any).checkRepliedIntentionQuery(chat.id)
        console.log(`   - 回复过意向调研: ${repliedIntentionQuery}`)
      } catch (error) {
        console.log(`   - 回复过意向调研: 检查失败 (${error.message})`)
      }
      
      // 测试进群状态
      try {
        const moerId = await DataService.getMoerIdByChatId(chat.id)
        const userId = moerId ? await DataService.getWxIdByMoerId(moerId) : null
        const hasJoinedGroup = userId ? await DataService.isInGroup(userId) : false
        console.log(`   - 已进群: ${hasJoinedGroup}`)
      } catch (error) {
        console.log(`   - 已进群: 检查失败 (${error.message})`)
      }
      
      console.log('\n' + '─'.repeat(60))
    }
    
    console.log('\n✅ 测试完成!')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
  }
}

// 运行测试
if (require.main === module) {
  testPreCourseIntentCalculation().catch(console.error)
}

export { testPreCourseIntentCalculation }
