#!/usr/bin/env ts-node

import { NewIntentCalculator } from '../bot/service/moer/components/signals/new_intent_score'
import { DataService } from '../bot/service/moer/getter/getData'
import { ChatStatStoreManager } from '../bot/service/moer/storage/chat_state_store'
import { ChatHistoryService } from '../bot/service/moer/components/chat_history/chat_history'

/**
 * 测试第二天和第三天意向度计算的脚本
 * 用于验证新的第二三天计算逻辑
 */

async function testDay23IntentCalculation() {
  try {
    console.log('🚀 开始测试第二天和第三天意向度计算...\n')

    // 获取测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('❌ 没有找到78期的客户')
      return
    }

    // 测试前2个客户
    const testChats = chats.slice(0, 2)
    
    for (const chat of testChats) {
      console.log(`\n📋 测试客户: ${chat.contact.wx_name} (${chat.id})`)
      console.log('=' .repeat(80))
      
      // 初始化客户状态
      await ChatStatStoreManager.initState(chat.id)
      
      // 获取课程开始时间
      const courseStartTime = await DataService.getCourseStartTime(chat.id)
      console.log(`📅 课程开始时间: ${courseStartTime}`)
      
      // 测试第二天
      await testDay2(chat.id, chat.contact.wx_name, courseStartTime)
      
      // 测试第三天
      await testDay3(chat.id, chat.contact.wx_name, courseStartTime)
      
      console.log('\n' + '─'.repeat(80))
    }
    
    console.log('\n✅ 第二三天意向度测试完成!')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
  }
}

async function testDay2(chatId: string, customerName: string, courseStartTime: Date) {
  console.log(`\n📊 第二天意向度测试`)
  console.log('-'.repeat(50))
  
  try {
    // 计算第二天意向度
    const day2Result = await NewIntentCalculator.calculateCourseIntentScore(chatId, 2)
    
    console.log(`📈 第二天意向度结果:`)
    console.log(`   总分: ${day2Result.intent_score} 分`)
    console.log(`   等级: ${day2Result.intent_level}`)
    console.log(`   基础数据:`)
    console.log(`   - 对话轮数: ${day2Result.course_data?.conversation_rounds}`)
    console.log(`   - 观看分钟数: ${day2Result.course_data?.course_watch_minutes}`)
    console.log(`   - 有反馈: ${day2Result.course_data?.replied_after_course_feeling}`)
    
    // 获取详细数据
    const day2Data = await (NewIntentCalculator as any).getDay2Data(chatId)
    
    console.log(`\n🔍 第二天详细分析:`)
    console.log(`   - 总回复数: ${day2Data.totalReplies}`)
    console.log(`   - 从未回复: ${day2Data.neverReplied}`)
    console.log(`   - 开课前对话比例: ${day2Data.preCourseConversationRatio.toFixed(2)}`)
    console.log(`   - 回复参加但未参加: ${day2Data.repliedJoinButNotJoin}`)
    console.log(`   - 给出不参加原因: ${day2Data.gaveReasonForNotJoining}`)
    console.log(`   - 完成课程: ${day2Data.courseCompleted}`)
    console.log(`   - 课程反馈: ${day2Data.courseFeedback}`)
    console.log(`   - 提到财富果园: ${day2Data.mentionedWealthGarden}`)
    console.log(`   - 第一二天都没看: ${day2Data.neverWatchedDay1And2}`)
    
    // 计算各项得分
    console.log(`\n📈 第二天得分明细:`)
    
    // 1. 开课前对话比例
    if (day2Data.preCourseConversationRatio > 1) {
      console.log(`   ✅ 开课前对话比例高: +10分`)
    } else {
      console.log(`   ❌ 开课前对话比例低: -10分`)
    }
    
    // 2. 当天完全没回复
    if (day2Data.totalReplies === 0) {
      console.log(`   ❌ 当天完全没回复: -10分`)
    } else {
      console.log(`   ✅ 当天有回复: 0分`)
    }
    
    // 3. 从未对话过
    if (day2Data.neverReplied) {
      console.log(`   ❌ 从未对话过: -10分`)
    } else {
      console.log(`   ✅ 有过对话: 0分`)
    }
    
    // 4. 回复参加但未参加
    if (day2Data.repliedJoinButNotJoin) {
      console.log(`   ❌ 回复参加但未参加: -10分`)
    } else {
      console.log(`   ✅ 没有虚假承诺: 0分`)
    }
    
    // 5. 给出不参加原因
    if (day2Data.gaveReasonForNotJoining) {
      console.log(`   ✅ 给出不参加原因: +5分`)
    } else {
      console.log(`   ❌ 未给出不参加原因: 0分`)
    }
    
    // 6. 看课进度
    if (day2Data.courseWatchMinutes >= 10) {
      console.log(`   ✅ 到课(≥10分钟): +10分`)
    } else {
      console.log(`   ❌ 未到课(<10分钟): 0分`)
    }
    
    if (day2Data.courseCompleted) {
      console.log(`   ✅ 完课: +10分`)
    } else {
      console.log(`   ❌ 未完课: 0分`)
    }
    
    // 7. 课程反馈
    const courseFeelingScore = (NewIntentCalculator as any).calculateCourseFeelingScore(day2Data.courseFeedback)
    console.log(`   📝 课程反馈(${day2Data.courseFeedback}): ${courseFeelingScore > 0 ? '+' : ''}${courseFeelingScore}分`)
    
    // 8. 财富果园画面
    if (day2Data.mentionedWealthGarden) {
      console.log(`   ✅ 提到财富果园画面: +10分`)
    } else {
      console.log(`   ❌ 未提到财富果园画面: 0分`)
    }
    
    // 9. 第一二天都没看
    if (day2Data.neverWatchedDay1And2) {
      console.log(`   ❌ 第一二天都没看: -10分`)
    } else {
      console.log(`   ✅ 至少看过一天: 0分`)
    }
    
  } catch (error) {
    console.error(`❌ 第二天计算失败:`, error)
  }
}

async function testDay3(chatId: string, customerName: string, courseStartTime: Date) {
  console.log(`\n📊 第三天意向度测试`)
  console.log('-'.repeat(50))
  
  try {
    // 计算第三天意向度
    const day3Result = await NewIntentCalculator.calculateCourseIntentScore(chatId, 3)
    
    console.log(`📈 第三天意向度结果:`)
    console.log(`   总分: ${day3Result.intent_score} 分`)
    console.log(`   等级: ${day3Result.intent_level}`)
    console.log(`   基础数据:`)
    console.log(`   - 对话轮数: ${day3Result.course_data?.conversation_rounds}`)
    console.log(`   - 观看分钟数: ${day3Result.course_data?.course_watch_minutes}`)
    console.log(`   - 有反馈: ${day3Result.course_data?.replied_after_course_feeling}`)
    
    // 获取详细数据
    const day3Data = await (NewIntentCalculator as any).getDay3Data(chatId)
    
    console.log(`\n🔍 第三天详细分析:`)
    console.log(`   - 总回复数: ${day3Data.totalReplies}`)
    console.log(`   - 从未回复: ${day3Data.neverReplied}`)
    console.log(`   - 前两天对话比例: ${day3Data.preTwoDaysConversationRatio.toFixed(2)}`)
    console.log(`   - 回复参加但未参加: ${day3Data.repliedJoinButNotJoin}`)
    console.log(`   - 给出不参加原因: ${day3Data.gaveReasonForNotJoining}`)
    console.log(`   - 完成课程: ${day3Data.courseCompleted}`)
    console.log(`   - 课程反馈: ${day3Data.courseFeedback}`)
    console.log(`   - 问询系统班: ${day3Data.inquiredAboutSystemClass}`)
    console.log(`   - 三天都没看: ${day3Data.neverWatchedDay123}`)
    
    // 计算各项得分
    console.log(`\n📈 第三天得分明细:`)
    
    // 1. 前两天对话比例
    if (day3Data.preTwoDaysConversationRatio > 1) {
      console.log(`   ✅ 前两天对话比例高: +10分`)
    } else {
      console.log(`   ❌ 前两天对话比例低: -10分`)
    }
    
    // 2. 当天完全没回复
    if (day3Data.totalReplies === 0) {
      console.log(`   ❌ 当天完全没回复: -10分`)
    } else {
      console.log(`   ✅ 当天有回复: 0分`)
    }
    
    // 3. 从未对话过
    if (day3Data.neverReplied) {
      console.log(`   ❌ 从未对话过: -10分`)
    } else {
      console.log(`   ✅ 有过对话: 0分`)
    }
    
    // 4. 回复参加但未参加
    if (day3Data.repliedJoinButNotJoin) {
      console.log(`   ❌ 回复参加但未参加: -10分`)
    } else {
      console.log(`   ✅ 没有虚假承诺: 0分`)
    }
    
    // 5. 给出不参加原因
    if (day3Data.gaveReasonForNotJoining) {
      console.log(`   ✅ 给出不参加原因: +10分`)
    } else {
      console.log(`   ❌ 未给出不参加原因: 0分`)
    }
    
    // 6. 看课进度
    if (day3Data.courseWatchMinutes >= 10) {
      console.log(`   ✅ 看课(≥10分钟): +10分`)
    } else {
      console.log(`   ❌ 未看课(<10分钟): 0分`)
    }
    
    if (day3Data.courseCompleted) {
      console.log(`   ✅ 看完: +10分`)
    } else {
      console.log(`   ❌ 未看完: 0分`)
    }
    
    // 7. 课程反馈
    const courseFeelingScore = (NewIntentCalculator as any).calculateDay3CourseFeelingScore(day3Data.courseFeedback)
    console.log(`   📝 课程反馈(${day3Data.courseFeedback}): ${courseFeelingScore > 0 ? '+' : ''}${courseFeelingScore}分`)
    
    // 8. 问询系统班
    if (day3Data.inquiredAboutSystemClass) {
      console.log(`   ✅ 问询系统班: +10分`)
    } else {
      console.log(`   ❌ 未问询系统班: 0分`)
    }
    
    // 9. 三天都没看
    if (day3Data.neverWatchedDay123) {
      console.log(`   ❌ 三天都没看: -20分`)
    } else {
      console.log(`   ✅ 至少看过一天: 0分`)
    }
    
  } catch (error) {
    console.error(`❌ 第三天计算失败:`, error)
  }
}

// 运行测试
if (require.main === module) {
  testDay23IntentCalculation().catch(console.error)
}

export { testDay23IntentCalculation }
