generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = "mongodb://root:free1234$spirit!@dds-bp100ccf7c2e7f741791-pub.mongodb.rds.aliyuncs.com:3717/moer?authSource=admin"
}

type ChatContact {
  wx_id   String
  wx_name String
}

type ChatState {
  slotAskedCount  Json
  nodeInvokeCount Json
  nextStage       String
  userSlots       Json
  state           Json
  progress        String
  danmuUserSlots  Json?
  moreUserSlots   Json?
  salesNote       String?
}

model chat {
  id                 String      @id @map("_id")
  contact            ChatContact
  round_ids          String[]
  wx_id              String
  is_human_involved  Boolean?
  created_at         DateTime?   @db.Date
  chat_state         ChatState
  course_no          Int?
  is_deleted         Boolean?
  moer_id            String?
  is_stop_group_push Boolean? // 停止群发
  is_test            Boolean?
  current_plan_id    String? // 当前活跃的计划ID
  pay_time           DateTime?

  @@index([moer_id])
  @@index([current_plan_id])
  @@index([course_no])
}

model chat_history {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  chat_id    String
  content    String
  created_at DateTime @db.Date
  role       String

  is_send_by_human  Boolean? // 是否人工回复
  short_description String? // SOP 描述
  round_id          String? // LLM 输出，会绑定 round_id
  sop_id            String?
  is_recalled       Boolean? // 撤回
  message_id        String? // 消息 ID
  chat_state        ChatState? // 产生回复时的 chat_state

  @@index([chat_id, created_at], map: "chat_id_1_created_at_1")
  @@index([message_id])
}

model account {
  id     String  @id @map("_id")
  name   String
  avatar String?
}

model sop_record {
  id                String @id @default(auto()) @map("_id") @db.ObjectId
  course_no         Int
  sop_id            String
  sent_count        Int
  reply_count_30min Int
  reply_count_60min Int
}

model log_store {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  chat_id   String?
  level     String
  timestamp DateTime @db.Date
  msg       String
}

model event_track {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  chat_id   String
  type      String
  meta      Json?
  timestamp DateTime @db.Date
}

model rag_map_oss {
  id                String @id @default(auto()) @map("_id") @db.ObjectId
  rag_resource_name String @unique
  oss_url           String
}

model user_count {
  id         String   @id @map("_id")
  count      Int
  created_at DateTime @db.Date
}

type Lesson {
  id           String // 墨尔的课程 ID
  name         String // 课程名
  index        Int // 课程 序号
  address      String // 上课地址
  is_recording Boolean // 是否是回放
}

model rag_supplement_questions {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  chat_id      String?
  timestamp    DateTime @db.Date
  scheduleTime Json
  database     String
  msg          String
}

model danmu {
  id       String    @id @default(auto()) @map("_id") @db.ObjectId
  content  String // 弹幕的内容
  time     Int // 弹幕出现的时间，单位是秒或毫秒
  liveId   String // 直播间的 ID
  sendTime DateTime? @db.Date // 发送弹幕的时间
  courseNo Int? // 期数
  day      Int? // 第几天的弹幕

  userId   String // 发送弹幕的用户 ID
  userName String // 发送弹幕的用户名
}

model config {
  id               String @id @default(auto()) @map("_id") @db.ObjectId
  enterpriseName   String
  accountName      String
  wechatId         String
  address          String
  port             String
  botUserId        String
  orgToken         String
  enterpriseConfig Json

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model message_annotation {
  id              String     @id @default(auto()) @map("_id") @db.ObjectId
  chat_id         String
  user_name       String?
  course_no       Int?
  chat_history_id String
  usr_message     String
  ai_message      String
  think           String?
  strategy        String?
  round_id        String?
  chat_state      ChatState?
  title           String?
  description     String?
  tags            String[]
  type            String
  created_at      DateTime   @db.Date

  @@index([chat_id])
}

model experience {
  id             String   @id @default(auto()) @map("_id") @db.ObjectId
  chat_id        String
  round_id       String
  user_message   String
  strategy       String
  created_at     DateTime @db.Date
  job            String
  pain           String
  goal           String
  course_status  String
  course_feeling String
  is_handled     Boolean
  is_add         Boolean
}

enum TaskStatus {
  TODO
  DOING
  DONE
  CANCELED
}

model task {
  id           String     @id @default(auto()) @map("_id") @db.ObjectId
  chat_id      String
  round_id     String?
  overall_goal String
  description  String
  status       TaskStatus @default(TODO)
  priority     Int        @default(0) // 越小越高
  created_at   DateTime   @default(now()) @db.Date
  completed_at DateTime?  @db.Date
  send_time    String? // 用于 debug 延迟任务

  @@index([chat_id])
  @@index([round_id])
  @@index([status])
  @@index([chat_id, status, priority, created_at])
}

model plan {
  id                 String   @id @default(auto()) @map("_id") @db.ObjectId
  created_at         DateTime @default(now()) @db.Date
  chat_id            String
  round_id           String
  overall_goal       String
  tasks              String[] // 任务列表
  status             String   @default("doing") // doing, completed
  current_task_index Int      @default(0) // 当前执行到哪一条任务

  @@index([chat_id])
  @@index([status])
}

model sop {
  id          String      @id @default(auto()) @map("_id") @db.ObjectId
  time_anchor String
  title       String
  week        Int
  day         Int
  time        String
  situations  Situation[]
  enable      Boolean
  tag         String
  topic       String
}

model sop_tag {
  id             String   @id @default(auto()) @map("_id") @db.ObjectId
  name           String
  enable         Boolean
  enable_account String[]
}

model sop_topic {
  id     String  @id @default(auto()) @map("_id") @db.ObjectId
  name   String
  enable Boolean
  tag    String
}

type Situation {
  conditions Condition[]
  action     Json[]
}

type Condition {
  // 逻辑是正还是负
  isOrNotIs Boolean
  type      String
  condition String
}
