/**
 * 示例程序：展示如何使用 MIPRO V2 优化器
 */

import { Example, LMProgram, Predictor, PredictorConfig, Metric } from './types'
import { LLM } from '../../llm/LLM'

/**
 * 简单的问答程序示例
 */
export class QuestionAnsweringProgram implements LMProgram {
  private predictors: { [name: string]: Predictor } = {}

  constructor() {
    // 初始化一个简单的问答预测器
    this.predictors['qa'] = {
      name: 'qa',
      signature: 'question -> answer',
      config: {
        instruction: 'Answer the question based on your knowledge.',
        demos: []
      }
    }
  }

  // 实现函数调用接口
  async call(input: Example): Promise<{ prediction: Example; trace: any }> {
    return this.predict(input)
  }

  // 实现 LMProgram 接口的函数调用
  async __call__(input: Example): Promise<{ prediction: Example; trace: any }> {
    return this.predict(input)
  }

  // 主要的预测方法
  async predict(input: Example): Promise<{ prediction: Example; trace: any }> {
    const qaPredictor = this.predictors['qa']
    const prompt = this.buildPrompt(qaPredictor.config, input)

    try {
      // 使用 LLM 进行预测
      const llm = new LLM({
        model: 'gpt-4.1',
        temperature: 0.1,
        max_tokens: 500
      })

      const response = await llm.predict(prompt)

      const prediction = {
        answer: response.trim()
      }

      const trace = {
        inputs: input,
        prompt: prompt,
        raw_response: response
      }

      return { prediction, trace }
    } catch (error) {
      console.error('Error in QA prediction:', error)
      return {
        prediction: { answer: 'Error: Unable to generate answer' },
        trace: { error: error instanceof Error ? error.message : String(error) }
      }
    }
  }

  updatePrompts(newPrompts: { [predictorName: string]: Partial<PredictorConfig> }): void {
    for (const [name, config] of Object.entries(newPrompts)) {
      if (this.predictors[name]) {
        if (config.instruction !== undefined) {
          this.predictors[name].config.instruction = config.instruction
        }
        if (config.demos !== undefined) {
          this.predictors[name].config.demos = config.demos
        }
      }
    }
  }

  getPredictors(): { [name: string]: Predictor } {
    return { ...this.predictors }
  }

  private buildPrompt(config: PredictorConfig, input: Example): string {
    const parts: string[] = []

    // 添加指令
    if (config.instruction) {
      parts.push(`<instructions>\n${config.instruction}\n</instructions>`)
      parts.push('')
    }

    // 添加示例
    if (config.demos.length > 0) {
      parts.push('<examples>')
      config.demos.forEach((demo, index) => {
        parts.push(`<example_${index + 1}>`)
        for (const [key, value] of Object.entries(demo)) {
          parts.push(`<${key}>${value}</${key}>`)
        }
        parts.push(`</example_${index + 1}>`)
      })
      parts.push('</examples>')
      parts.push('')
    }

    // 添加当前输入
    parts.push('<input>')
    for (const [key, value] of Object.entries(input)) {
      parts.push(`<${key}>${value}</${key}>`)
    }
    parts.push('</input>')
    parts.push('')
    parts.push('Please provide your answer:')

    return parts.join('\n')
  }
}

/**
 * 简单的精确匹配评估指标
 */
export const exactMatchMetric: Metric = async (prediction: Example, groundTruth: Example): Promise<number> => {
  const predAnswer = String(prediction.answer || '').toLowerCase().trim()
  const trueAnswer = String(groundTruth.answer || '').toLowerCase().trim()

  return predAnswer === trueAnswer ? 1.0 : 0.0
}

/**
 * 基于包含关系的评估指标
 */
export const containsMetric: Metric = async (prediction: Example, groundTruth: Example): Promise<number> => {
  const predAnswer = String(prediction.answer || '').toLowerCase().trim()
  const trueAnswer = String(groundTruth.answer || '').toLowerCase().trim()

  if (predAnswer.includes(trueAnswer) || trueAnswer.includes(predAnswer)) {
    return 1.0
  }

  return 0.0
}

/**
 * 基于 LLM 的语义相似度评估指标
 */
export const semanticSimilarityMetric: Metric = async (prediction: Example, groundTruth: Example): Promise<number> => {
  const predAnswer = String(prediction.answer || '').trim()
  const trueAnswer = String(groundTruth.answer || '').trim()

  if (!predAnswer || !trueAnswer) {
    return 0.0
  }

  try {
    const llm = new LLM({
      model: 'gpt-4.1-mini',
      temperature: 0,
      max_tokens: 10
    })

    const prompt = `Rate the semantic similarity between these two answers on a scale of 0.0 to 1.0:

Answer 1: ${predAnswer}
Answer 2: ${trueAnswer}

Respond with only a number between 0.0 and 1.0:`

    const response = await llm.predict(prompt)
    const score = parseFloat(response.trim())

    return isNaN(score) ? 0.0 : Math.max(0.0, Math.min(1.0, score))
  } catch (error) {
    console.warn('Error in semantic similarity evaluation:', error)
    return 0.0
  }
}

/**
 * 创建示例训练数据集
 */
export function createSampleDataset(): Example[] {
  return [
    {
      question: 'What is the capital of France?',
      answer: 'Paris'
    },
    {
      question: 'Who wrote Romeo and Juliet?',
      answer: 'William Shakespeare'
    },
    {
      question: 'What is 2 + 2?',
      answer: '4'
    },
    {
      question: 'What is the largest planet in our solar system?',
      answer: 'Jupiter'
    },
    {
      question: 'In what year did World War II end?',
      answer: '1945'
    },
    {
      question: 'What is the chemical symbol for gold?',
      answer: 'Au'
    },
    {
      question: 'Who painted the Mona Lisa?',
      answer: 'Leonardo da Vinci'
    },
    {
      question: 'What is the speed of light in vacuum?',
      answer: '299,792,458 meters per second'
    },
    {
      question: 'What is the smallest unit of matter?',
      answer: 'Atom'
    },
    {
      question: 'Which ocean is the largest?',
      answer: 'Pacific Ocean'
    }
  ]
}
