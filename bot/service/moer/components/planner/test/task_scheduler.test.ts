// import { TaskScheduler, ScheduleTaskInput, ScheduleTaskOutput } from '../task_scheduler'
// import logger from '../../../../../model/logger/logger'
//
// describe('TaskScheduler', () => {
//   let scheduler: TaskScheduler
//
//   beforeEach(() => {
//     scheduler = new TaskScheduler()
//   })
//
//   describe('正例测试', () => {
//     it('应该正确识别和调度混合任务类型', async () => {
//       const input: ScheduleTaskInput = {
//         current_time: '2025-06-17 11:00:00',
//         tasks_to_schedule: [
//           {
//             task_id: 'Task-001',
//             task_description: '客户刚刚分享了Day 1课程的感受，反馈说\'容易走神\'，需要进行共情和解读。'
//           },
//           {
//             task_id: 'Task-002',
//             task_description: '发送Day 2课程《财富唤醒》的上课提醒，课程将在今晚20:00开始。'
//           },
//           {
//             task_id: 'Task-003',
//             task_description: '明天早上问候客户。'
//           }
//         ],
//         existing_schedule: [
//           {
//             time: '2025-06-17 19:45:00',
//             description: '发送最后15分钟的开课冲刺提醒。'
//           }
//         ]
//       }
//
//       const result = await scheduler.scheduleTask(input)
//
//       logger.log('正例测试结果:', JSON.stringify(result, null, 2))
//
//       // 验证结果结构
//       expect(result).toHaveProperty('step1_identification_result')
//       expect(result).toHaveProperty('step2_scheduling_plan')
//       // expect(result.step1_identification_result).toHaveProperty('proactive_tasks')
//       // expect(result.step1_identification_result).toHaveProperty('reactive_materials')
//
//       // // 验证每个任务都有推理
//       // result.step1_identification_result.proactive_tasks.forEach((task) => {
//       //   expect(task).toHaveProperty('reasoning')
//       //   expect(task).toHaveProperty('task_id')
//       //   expect(task.reasoning).toBeTruthy()
//       // })
//       //
//       // result.step1_identification_result.reactive_materials.forEach((task) => {
//       //   expect(task).toHaveProperty('reasoning')
//       //   expect(task).toHaveProperty('task_id')
//       //   expect(task.reasoning).toBeTruthy()
//       // })
//       //
//       // // 验证调度计划
//       // result.step2_scheduling_plan.forEach((plan) => {
//       //   expect(plan).toHaveProperty('task_id')
//       //   expect(plan).toHaveProperty('urgency_level')
//       //   expect(plan).toHaveProperty('task_type')
//       //   expect(plan).toHaveProperty('scheduled_time')
//       //   expect(['urgent', 'normal']).toContain(plan.urgency_level)
//       //   expect(['daily_greeting', 'pre_class_reminder', 'post_class_follow_up', 'engagement_prompt', 'value_delivery']).toContain(plan.task_type)
//       // })
//     }, 60000)
//
//     it('应该正确处理紧急任务', async () => {
//       const input: ScheduleTaskInput = {
//         current_time: '2025-06-17 14:30:00',
//         tasks_to_schedule: [
//           {
//             task_id: 'Urgent-001',
//             task_description: '客户刚刚完成了课程，需要立即发送感谢和鼓励消息。'
//           },
//           {
//             task_id: 'Normal-001',
//             task_description: '分享今日冥想金句给客户。'
//           }
//         ],
//         existing_schedule: []
//       }
//
//       const result = await scheduler.scheduleTask(input)
//
//       logger.log('紧急任务测试结果:', JSON.stringify(result, null, 2))
//
//       // 应该有紧急任务被标记为 "now" 或者 urgency_level 为 "urgent"
//       // const urgentTasks = result.step2_scheduling_plan.filter((plan) =>
//       //   plan.scheduled_time === 'now' || plan.urgency_level === 'urgent')
//       // expect(urgentTasks.length).toBeGreaterThan(0)
//     }, 60000)
//
//     it('应该正确处理课程相关任务', async () => {
//       const input: ScheduleTaskInput = {
//         current_time: '2025-06-17 15:00:00',
//         tasks_to_schedule: [
//           {
//             task_id: 'Course-001',
//             task_description: '提醒客户今晚20:00的《财富能量》课程即将开始。'
//           },
//           {
//             task_id: 'Course-002',
//             task_description: '昨天课程结束后，询问客户的学习感受和收获。'
//           }
//         ],
//         existing_schedule: [
//           {
//             time: '2025-06-17 19:30:00',
//             description: '发送课前准备指导。'
//           }
//         ]
//       }
//
//       const result = await scheduler.scheduleTask(input)
//
//       logger.log('课程任务测试结果:', JSON.stringify(result, null, 2))
//
//       // 验证课程相关任务类型
//       const courseReminders = result.filter((plan) => plan.task_type === 'pre_class_reminder')
//       const followUps = result.filter((plan) => plan.task_type === 'post_class_follow_up')
//
//       expect(courseReminders.length + followUps.length).toBeGreaterThan(0)
//     }, 60000)
//   })
//
//   describe('边界情况测试', () => {
//     it('应该处理空任务列表', async () => {
//       const input: ScheduleTaskInput = {
//         current_time: '2025-06-17 10:00:00',
//         tasks_to_schedule: [],
//         existing_schedule: []
//       }
//
//       const result = await scheduler.scheduleTask(input)
//
//       logger.log('空任务列表测试结果:', JSON.stringify(result, null, 2))
//
//       expect(result).toHaveLength(0)
//     }, 60000)
//
//     it('应该处理密集的已有调度', async () => {
//       const input: ScheduleTaskInput = {
//         current_time: '2025-06-17 09:00:00',
//         tasks_to_schedule: [
//           {
//             task_id: 'Dense-001',
//             task_description: '发送早安问候。'
//           },
//           {
//             task_id: 'Dense-002',
//             task_description: '分享冥想小贴士。'
//           }
//         ],
//         existing_schedule: [
//           { time: '2025-06-17 09:15:00', description: '已有任务1' },
//           { time: '2025-06-17 09:45:00', description: '已有任务2' },
//           { time: '2025-06-17 10:15:00', description: '已有任务3' },
//           { time: '2025-06-17 10:45:00', description: '已有任务4' }
//         ]
//       }
//
//       const result = await scheduler.scheduleTask(input)
//
//       logger.log('密集调度测试结果:', JSON.stringify(result, null, 2))
//
//       // 验证新任务不会与已有任务冲突（至少30分钟间隔）
//       result.forEach((plan) => {
//         if (plan.scheduled_time !== 'now') {
//           const planTime = new Date(plan.scheduled_time)
//           input.existing_schedule.forEach((existing) => {
//             const existingTime = new Date(existing.time)
//             const timeDiff = Math.abs(planTime.getTime() - existingTime.getTime())
//             expect(timeDiff).toBeGreaterThanOrEqual(1 * 60 * 1000) // 30分钟
//           })
//         }
//       })
//     }, 60000)
//   })
//
//   describe('反例测试', () => {
//     it('应该正确区分主动和被动任务', async () => {
//       const input: ScheduleTaskInput = {
//         current_time: '2025-06-17 12:00:00',
//         tasks_to_schedule: [
//           {
//             task_id: 'Reactive-001',
//             task_description: '准备一段安慰话术，用于客户表达焦虑时的回复。'
//           },
//           {
//             task_id: 'Reactive-002',
//             task_description: '整理常见问题的标准答案，备用回复素材。'
//           },
//           {
//             task_id: 'Proactive-001',
//             task_description: '主动发送课程学习进度报告给客户。'
//           }
//         ],
//         existing_schedule: []
//       }
//
//       const result = await scheduler.scheduleTask(input)
//
//       logger.log('主动被动区分测试结果:', JSON.stringify(result, null, 2))
//
//       // 验证被动任务不应该出现在调度计划中
//       // const reactiveTaskIds = result.reactive_materials.map((task) => task.task_id)
//       // const scheduledTaskIds = result.map((plan) => plan.task_id)
//
//       // reactiveTaskIds.forEach((reactiveId) => {
//       //   expect(scheduledTaskIds).not.toContain(reactiveId)
//       // })
//
//       // // 验证主动任务应该出现在调度计划中
//       // const proactiveTaskIds = result.step1_identification_result.proactive_tasks.map((task) => task.task_id)
//       // proactiveTaskIds.forEach((proactiveId) => {
//       //   expect(scheduledTaskIds).toContain(proactiveId)
//       // })
//     }, 60000)
//
//     it('应该处理模糊任务描述', async () => {
//       const input: ScheduleTaskInput = {
//         current_time: '2025-06-17 16:00:00',
//         tasks_to_schedule: [
//           {
//             task_id: 'Ambiguous-001',
//             task_description: '处理客户的事情。'
//           },
//           {
//             task_id: 'Ambiguous-002',
//             task_description: '做一些跟进工作。'
//           }
//         ],
//         existing_schedule: []
//       }
//
//       const result = await scheduler.scheduleTask(input)
//
//       logger.log('模糊任务测试结果:', JSON.stringify(result, null, 2))
//
//       // // 即使任务描述模糊，也应该能够分类和调度
//       // expect(result.step1_identification_result.proactive_tasks.length +
//       //        result.step1_identification_result.reactive_materials.length).toBe(2)
//     }, 60000)
//   })
// })


import { PrismaMongoClient } from '../../../../../model/mongodb/prisma'
import { TaskManager } from '../task/task_manager'
import { ota } from 'zod/dist/types/v4/locales'
import { Planner } from '../plan/planner'
import { Config } from '../../../../../config/config'
import { Job, Queue } from 'bullmq'
import { RedisDB } from '../../../../../model/redis/redis'
import { loadConfigByAccountName } from '../../../../../../test/tools/load_config'
import { RedisCacheDB } from '../../../../../model/redis/redis_cache'
import { ChatStatStoreManager } from '../../../storage/chat_state_store'
import { DataService } from '../../../getter/getData'
import { ChatHistoryService } from '../../chat_history/chat_history'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    // 获取 77 期  moer1
    const chats = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        course_no: 77,
        wx_id: '****************'
      }
    })

    for (const chat of chats) {
      const chatId = chat.id
      const tasks = await TaskManager.getActiveTasks(chatId)

      if (tasks.length > 5) {
        console.log('fk')
      }

      // await TaskManager.cancelTasks(tasks.map((task) => task.id))
    }

  }, 1E8)


  it('123123', async () => {
    const tasks = await TaskManager.getActiveTasks('7881302560909397_****************')


    console.log(tasks)
  }, 30000)


  it('看下队列里任务', async () => {
    Config.setting.wechatConfig = await loadConfigByAccountName('qiaoqiao')

    const queueName = Planner.getPlannerSOPQueueName(Config.setting.wechatConfig?.id as string)

    const queue = new Queue(queueName, {
      connection: RedisDB.getInstance()
    })

    console.log(JSON.stringify(await queue.getDelayed(), null, 4))
  }, 30000)


  it('queue Task', async () => {
    Config.setting.wechatConfig = await loadConfigByAccountName('qiaoqiao')

    const queueName = Planner.getPlannerSOPQueueName(Config.setting.wechatConfig?.id as string)

    const queue = new Queue(queueName, {
      connection: RedisDB.getInstance()
    })


    const waitingJobs: Job[] = await queue.getDelayed()

    console.log(`总共获取到 ${waitingJobs.length} 个任务`)

    // 用于跟踪已见过的任务名称
    const seenNames = new Set<string>()
    const keptJobs: Job[] = []
    const removedJobs: Job[] = []

    // 遍历所有任务，对重复的name只保留第一条
    for (const job of waitingJobs) {
      const jobName = job.name

      if (!seenNames.has(jobName)) {
        // 第一次见到这个名称，保留
        seenNames.add(jobName)
        keptJobs.push(job)
        console.log(`保留任务: ${jobName} (ID: ${job.id})`)
      } else {
        // 重复的名称，删除
        await job.remove()
        removedJobs.push(job)
        console.log(`删除重复任务: ${jobName} (ID: ${job.id})`)
      }
    }

    console.log('\n处理结果:')
    console.log(`- 保留任务数: ${keptJobs.length}`)
    console.log(`- 删除任务数: ${removedJobs.length}`)
    console.log(`- 唯一任务名称: ${Array.from(seenNames).join(', ')}`)

    // 显示保留的任务详情
    console.log('\n保留的任务详情:')
    console.log(JSON.stringify(keptJobs.map((job) => ({
      id: job.id,
      name: job.name,
      data: job.data
    })), null, 2))
  }, 60000)
})