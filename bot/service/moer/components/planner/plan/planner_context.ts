import { ChatHistoryService } from '../../chat_history/chat_history'
import { tool } from '@langchain/core/tools'
import { z } from 'zod'
import { LLM } from '../../../../../lib/ai/llm/LLM'
import { DataService } from '../../../getter/getData'
import { MoerGeneralRAG } from '../../rag/moer_general'
import dayjs from 'dayjs'
import { XMLHelper } from '../../../../../lib/xml/xml'
import logger from '../../../../../model/logger/logger'
import { SalesNote } from '../../sales_note/sales_note'
import { ContextBuilder } from '../../agent/context'

export class PlannerContext {
  public static separator = '##'

  /**
   * 聊天记录
   * 客户槽位
   * Planner RAG
   * 销售小记
   * Stage Prompt
   * 当前说的话
   * @param chat_id
   * @param userMessage
   * @param round_id
   */
  public static async build(chat_id: string, userMessage: string, round_id: string) {
    const chatHistory = await this.getChatHistory(chat_id, userMessage)
    const plannerRAG = await this.getPlannerRAG(chat_id, userMessage, round_id)
    // const salesNote = await this.getSalesNote(chat_id, round_id)
    const customerPortrait = await ContextBuilder.getCustomerPortrait(chat_id)

    const stagePromptData = ContextBuilder.getBriefStagePromptData()
    const stagePrompt = await ContextBuilder.getStagePrompt(chat_id, stagePromptData)
    const userEvents = await ContextBuilder.getCustomerBehavior(chat_id)

    return [
      this.wrapContext('对话历史', chatHistory),
      this.wrapContext('补充知识', plannerRAG),
      this.wrapContext('客户画像',  customerPortrait),
      `${stagePrompt}\n${userEvents}`,
      this.wrapContext('客户发言', userMessage)
    ].join('\n').trim()
  }

  public static wrapContext(title: string, context: string) {
    if (!context) {
      return ''
    }

    return `${this.separator} ${title}
${context}`
  }

  public static async getChatHistory(chat_id: string, userMessage: string) {
    const chatHistory = await ChatHistoryService.getRecentLLMChatHistory(chat_id, 3)
    // 删除最后一条重复的 userMessage
    if (chatHistory[chatHistory.length - 1].getType() === 'human' && chatHistory[chatHistory.length - 1].content === userMessage) {
      chatHistory.pop()
    }
    return chatHistory.map((item) => `${item.getType() === 'human' ? '客户' : '助教老师'}: ${ item.content}`).join('\n')
  }

  public static async getPlannerRAG(chat_id: string, userMessage: string, round_id: string) {
    /**
     * Tool 1: 查询入门营课程信息
     * 职责：获取5天免费体验营的课程细节。
     */
    const getEntryCampDetail = tool(
      async ({ courseName }) => {
        return `客户正在参与的课程

## 5天入门营（当前课程）
- 小讲堂：介绍后续3天课程的主要内容，普及冥想的作用，带领大家体验一次放松身心的海浪减压冥想，完成后会给客户发送完课礼《冥想练习指南》
- 能量测评：通过大卫·霍金斯的能量测评表，帮助同学清晰了解自己的能量状态，为后续课程的针对性学习打下基础。这部分内容会在小讲堂中详细讲解
- 开营仪式：周日20:00在社群内进行图文介绍，包括本次课程的授课导师信息、课程内容与收获，上课地点，以及如何提升学习效果的小技巧
- 第一课：周一20:00直播，主题为情绪减压。解析情绪和睡眠问题的根本原因，并手把手教大家冥想的正确姿势。带练冥想【沉浸式秒睡】，对缓解情绪压力和改善睡眠质量效果显著
- 第二课：周二20:00直播，主题为财富唤醒。聚焦于【财富问题】，解析富足的本质和吸引力法则，帮助学员逐步找到迷茫、负债等问题的内在原因。通过【财富果园】冥想带练，扫清对物质与财富的心理障碍，培养轻松获取和管理财富的正向思维
- 第三课：周三20:00直播，主题为红靴子飞跃。这是课程中最核心的一课，专注提升专注力和内在能量。带练冥想【红靴子飞跃】，帮助学员稳定内心、激发潜能。老师强调，如果一生只练一个冥想，红靴子冥想是最佳选择`
      },
      {
        name: 'get_entry_camp_detail',
        description: '【仅用于查询5天免费入门体验营】的课程信息。当需要将客户的初期体验或需求与某节入门营课程连接时调用。第一节课 《情绪减压》：让你心身放松，从情绪根源释放压力，沉浸式秒睡冥想练习。 第二天练习《财富解锁》：让你突破金钱的卡点，唤醒你的财富潜力，有财富果园练习。 第三天练习《效能提升》：《红靴子》冥想，让你做事更高效，增强能量。 第四节课 《蓝鹰预演》帮助清晰愿景，无限数息，延长呼吸',
        schema: z.object({
          courseName: z.string().describe('要查询的入门营课程的相关信息'),
        }),
      }
    )

    const systemStartTime = await DataService.getSystemCourseStartTime(chat_id)

    /**
     * Tool 2: 查询系统班（付费商品）信息
     * 职责：获取21天付费系统班的详细信息。
     */
    const getSystemCourseDetail = tool(
      async ({ query }) => {
        return  `## 21天系统班权益  
- 学习方式：支持永久回放，学习时间灵活。
- 延期政策：支持延期至后续月份开课，但优惠价格仅限当期。
- 退款保障：开课后3天内不满意，支持全额退款。

## 21天系统班（后续课程）
- 系统班是三周21天的沉浸式旅程 ，每周5天课程稳步深入，每节课60～90分钟左右。是老师入门的开山之作。唐宁老师亲自带练。老师的目标是这12套功法要练一辈子
- 对零基础小白超友好：老师最骄傲的入门体系，过往的课程的回看有效期是仅仅只有一年，本次直接赠送永久回放，一次投入，终身复习，性价比拉满
- 正式课学习内容包含传承了千年的12套功法，将教会大家冥想3要素：体式，呼吸，念头
  - 禅宗三法：坐禅、立禅、卧禅，开启觉知、清零负能，攻克体式，进入不了状态的卡点。启动觉知、清空负能，解决“姿势找不到感觉”的难题
  - 五大呼吸：地火水风空五大呼吸，感受呼吸带来的能量，正能滋养，攻克呼吸短，浅，憋的卡点
  - 四大觉禅：音、光、息、念的冥想，更加精进，高能增频
- 价格：原价2880元，直播期间优惠1000元，现价1880元，是第一次有这样的优惠价格
- 赠品：限量赠送价值399元的采用抗震防潮材质的唐宁老师同款坐垫 (坐垫所剩不多)，价值144元的墨尔App季卡（含一百多个冥想音频）和21天系统班总结笔记。是老师四十多种课程里性价比最高的课程
- 智能直播加永久回放：每周提供四节课程和一节直播答疑，确保灵活学习
  - 系统化练习：周末设定练习打卡日，每天40-60分钟，帮你把核心心法真正落地到生活
  - 1对1助教跟进：全程关注你的进度，随时个性化答疑与指导
  - 学习社群：伙伴互助，共同进阶，修行不再孤单
- 21天课程系统班的开课时间：${systemStartTime}，如果时间来不及话可以申请延期`
      },
      {
        name: 'get_system_course_detail',
        description: '【仅用于查询21天付费系统班（商品）】的信息。当客户进入转化阶段，开始询问系统班细节、价格、政策，或需要处理相关异议时调用。',
        schema: z.object({
          query: z.string().describe('要查询的系统班相关主题'),
        }),
      }
    )

    /**
     * Tool 3: 查询通用FAQ
     * 职责：解答与课程内容无关的通用性问题。
     */
    const getFaqAnswer = tool(
      async ({ query }) => {
        return await MoerGeneralRAG.search(query, chat_id, round_id)
      },
      {
        name: 'get_faq_answer',
        description: '查询通用的、与具体课程内容无关的常见问题。例如登录问题、时间安排等。',
        schema: z.object({
          query: z.string().describe('要查询的通用问题关键词'),
        }),
      }
    )

    const tools = [getEntryCampDetail, getSystemCourseDetail, getFaqAnswer]
    const llm = new LLM()

    // 1. 获取开营时间
    const courseStartTime = await DataService.getCourseStartTime(chat_id)

    // 2. 加 2 天
    const salesStartDate = dayjs(courseStartTime).add(2, 'day').format('YYYY-MM-DD HH:mm:ss')

    const systemPromptForToolCall = `# 背景信息
你是一个AI销售Planner的智能信息助理。你的任务是判断Planner在当前节点是否需要查询知识库，如果需要，就调用合适的工具。
- **当前时间**: ${new Date().toLocaleString()} 
- **销售流程**: 我们正在进行一个为期5天的冥想入门体验营。前3天（截止到 ${salesStartDate} 之前）是纯粹的体验和价值建立期。从第3天晚上（${salesStartDate} 开始）会正式介绍并售卖21天付费系统班（我们的核心商品）。

# 工具使用规则 (非常重要)
1.  **阶段性约束**:
    -   **在销售开始前 (当前时间 < ${salesStartDate})**: 【绝对禁止】调用 \`get_system_course_detail\` 工具。此时你的所有注意力都应该放在入门营体验上。
    -   **在销售开始后 (当前时间 >= ${salesStartDate})**: 你【可以】调用 \`get_system_course_detail\` 来处理与付费课程相关的咨询和异议。
2.  **工具职责区分**:
    -   \`get_entry_camp_detail\`: 只用来查5天免费体验营的课程。
    -   \`get_system_course_detail\`: 只用来查21天付费系统班（商品）的信息。
    -   \`get_faq_answer\`: 只用来查通用的、常见的一些问题答疑。
3.  **调用时机**: 
    -   **优先调用**: 当客户的需求或问题与某个【具体课程主题】或【FAQ关键词】高度匹配时，你应该【主动调用工具】来获取精准信息，以便Planner制定出更具说服力的个性化策略。
    -   **调用目的**: 调用工具的目的是为了【丰富规划的细节和依据】，而不是等到万不得已。例如，知道“财富解锁”课程的具体内容，可以帮助Planner更好地将其与客户的“财富焦虑”联系起来。
    -   **避免调用**: 如果客户的意图非常模糊，或者当前首要任务是进行情感共情而非信息传递，则可以回答“无需调用工具”。

# 你的任务
基于以上背景和规则，分析下面的对话信息，并决定是否以及如何调用工具。如果不需要调用，直接回答“无需调用工具”。`

    // 2. 将客户信息作为User Message传递
    const userPromptForToolCall = `**最近对话历史:**
${await this.getChatHistory(chat_id, userMessage)}

**当前客户发言:**
${userMessage}

请根据你的分析，进行工具调用或回答“无需调用工具”。`


    // 3. Call toolCall
    return await llm.toolCall(tools, userPromptForToolCall, systemPromptForToolCall)
  }

  public static async getSalesNote(chat_id: string, round_id: string) {
    const stagePromptData = ContextBuilder.getBriefStagePromptData()
    const stagePrompt = await ContextBuilder.getStagePrompt(chat_id, stagePromptData)
    const userSlots = await ContextBuilder.getCustomerPortrait(chat_id)
    const chatHistory = ChatHistoryService.formatHistoryHelper(await ChatHistoryService.getRecentConversations(chat_id, 5, 'user'))

    const prompt = `你是一个资深B2C的销售专家，你在观察客户和销售的对话，客户是在各个平台看到投流冥想的0元或者5元的学习广告，继而加到我们的微信做学习。忠实洞察客户当前销售状态，为销售跟进提供客户侧的洞察。

${stagePrompt}

${userSlots}

## 之前销售小记：
${await SalesNote.getByChatId(chat_id)}

## 具体的对话记录：
${chatHistory}

主要任务
(1)根据已知的客户具体画像，所处课程阶段以及当前的对话，按照 PABRC 的销售逻辑得出当前客户的销售洞察。

注意：如果缺乏信息不足以推理，直接备注需要补充哪部分的信息。信息不充分的条件下不用强行推理。

P：画像定位：回答目前面对的是谁，了解行业，角色，认知水平、潜在动机、付费能力
A：痛点放大：现状哪儿疼，有多疼？评估客户认知下解决这个问题紧急程度，如果还未了解清楚可以进一步挖掘
B：价值桥梁(如果还不清楚客户画像和痛点可以不输出):客户目前感受到课程价值是否合理，有什么我需要额外补充介绍，让其明确 before & after 的区别
R：风险拆除：还有什么阻碍他得行动？列表化所有可能异议（钱、时间、信任…）→ 逐一提前布雷或现场化解
C: 促成成交：设计清晰 CTA

(2) 基于PABRC的分析，精要总结客户销售阶段（客户类型，信任阶段，痛点紧急程度，正式课程学习感受，缺少的信息等，关键假设处理优先级）

输出格式：
<think>
{
  "P_画像定位": "...",
  "A_痛点放大": "...",
  "B_价值桥梁": "...",
  "R_风险拆除": "...",
  "C_促成成交": "..."
}
</think>

<summary>
{
    "客户类型": "...",
    "信任阶段": "...",
    "痛点紧急程度": "...",
    "正式课程学习感受": "...",
    "缺少的信息/关键假设处理优先级": "..."
}
</summary>`


    const llm = new LLM({ promptName: 'get_sales_note', meta: { chat_id: chat_id, round_id: round_id } })
    const llmRes = await llm.predict(prompt)
    console.log(llmRes)

    const think = XMLHelper.extractContent(llmRes, 'think') || ''
    const keyPointsSummary = XMLHelper.extractContent(llmRes, 'summary') || ''

    logger.log({ chat_id }, '销售小记\n', think, keyPointsSummary)

    await SalesNote.update(chat_id, keyPointsSummary)
    return keyPointsSummary
  }
}