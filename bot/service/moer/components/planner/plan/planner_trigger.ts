import { LLM } from '../../../../../lib/ai/llm/LLM'
import { XMLHelper } from '../../../../../lib/xml/xml'
import logger from '../../../../../model/logger/logger'
import { ChatPromptTemplate } from '@langchain/core/prompts'
import { PlannerTriggerInput, PlannerTriggerOutput } from '../types'


interface IPlannerInitParams {
  model?: string
  round_id?: string
  chat_id?: string
}

/**
 * Planner 触发检测器
 * 负责判断当前对话是否需要触发战略规划
 */
export class PlannerTrigger {
  private llm: LLM

  constructor(param: IPlannerInitParams) {
    if (!param.model) {
      param.model = 'gpt-4.1-mini'
    }

    this.llm = new LLM({
      model: param.model,
      meta: {
        chat_id: param.chat_id,
        round_id: param.round_id,
      },
      promptName: 'planner_trigger'
    })
  }

  /**
   * 检测是否需要触发 Planner
   * @param input 输入参数
   * @returns 检测结果
   */
  public async detectTrigger(input: PlannerTriggerInput): Promise<PlannerTriggerOutput> {
    try {
      const { conversationHistory, currentUserMessage, userProfile } = input

      // 创建 prompt template
      const promptTemplate = this.createPromptTemplate()

      // 生成 prompt
      const prompt = await promptTemplate.invoke({
        conversationHistory,
        currentUserMessage,
        userProfile
      })

      // 调用 LLM
      const response = await this.llm.predict(prompt.toString())

      // 解析响应
      return this.parseXmlResponse(response)

    } catch (error) {
      logger.error('PlannerTriggerDetector error:', error)

      // 返回默认值，不触发 Planner
      return {
        reasoning: '检测过程中发生错误，默认不触发 Planner',
        isPlannerTrigger: false
      }
    }
  }

  /**
   * 创建 Prompt Template
   */
  private createPromptTemplate(): ChatPromptTemplate {
    return ChatPromptTemplate.fromMessages([
      ['system', `你是一个专业的销售对话分析师，负责判断客户消息是否需要触发战略规划的分类器。
当前销售的商品是：墨尔冥想5天入门营后的21天高阶课程

分析对话历史和当前客户消息，判断当前是否是触发需要复杂推理的战略规划的时间点。

## 战略规划的触发条件包括：
1. 客户身份与痛点识别 - 相对比已有客户画像，客户是否主动或被动透露了新的身份特征、当前生活/工作面临的具体挑战，以及本次学习冥想的目标
2. 销售时机或铺垫后续成交的时机 - 可能会对影响客户对课程价值感受的场景，例如上课感受异议，课程效果疑惑等问题
3. 成交风险 - 可能是成交1880高阶课程(系统班)的卡点风险要素：撞课，时间投入，费用，担心学不会等问题
4. 提问复杂性 - 客户的提问是否复杂、模糊，或需要多步引导才能解决其疑虑

请分析并输出：

<reasoning>
[详细分析客户消息的内容，判断是否符合触发条件，说明你的推理过程]
</reasoning>

<isPlannerTrigger>
[true 或 false，表示是否需要触发战略规划]
</isPlannerTrigger>`],

      // Few-shot examples
      ['user', `对话历史：
assistant: 您目前的生活角色？ 1-职场奋斗者... 您的冥想经验值？ 5-纯小白... 最想点亮的人生议题? 8-情绪减压...

当前客户消息：退休精进者, 纯小白, 财富能量`],

      ['assistant', `<reasoning>
客户主动提供了自己的生活角色（退休精进者）、冥想经验（纯小白）以及最关注的人生议题（财富能量），这些都是关键的个人画像信息。此类信息直接影响后续的内容推荐、沟通风格和目标设定，属于战略层面需要调整的核心输入。
</reasoning>

<isPlannerTrigger>
true
</isPlannerTrigger>`],

      ['user', `对话历史：
assistant: 匮乏记忆驱动：过去经历的"努力未被承认"或"成果被夺走"可能在你心里留下痕迹... 您觉得您目前是哪一种？

当前客户消息：前年，电诈骗了我九万多`],

      ['assistant', `<reasoning>
客户暴露了一个重大的创伤事件——电信诈骗造成的巨额经济损失。这不仅是具体的外部阻力，更重要的是可能带来深层的心理创伤，如信任危机、安全感缺失等。这种经历会深刻影响客户的行为模式和心理状态，是理解其所有后续反应的核心。
</reasoning>

<isPlannerTrigger>
true
</isPlannerTrigger>`],

      ['user', `对话历史：
assistant: 是的 这个是正常的
user: 冥想助教-大米老师： 你的这些感受在冥想中是完全正常的，尤其是像红靴子冥想这种深度的练习，它会调动身体的能量，帮助你释放积压的情绪和身体上的紧张。我可以给你一些解读和建议
assistant: 冥想助教-大米老师： 你需要不

当前客户消息：最近报名了其他的，忙不过来`],

      ['assistant', `<reasoning>
客户提出了具体的成交风险——已经报名了其他课程，导致时间和精力不足。这是一个明确的、非主观的卡点风险，会直接影响客户的参与度和学习效果。这种情况需要重新评估策略，制定更灵活的参与方案。
</reasoning>

<isPlannerTrigger>
true
</isPlannerTrigger>`],

      ['user', `对话历史：
assistant: ...现在，也许最重要的不是再去"干活"，而是允许自己收获
user: 收到！谢谢老师的解读🙏
assistant:: 客气啦 您财富果园这节课显示出来的部分 特别适合在今晚八点的红靴子课程里面被得到解决...

当前客户消息：老师！如何做到？允许自己收获`],

      ['assistant', `<reasoning>
客户针对"允许自己收获"提出了深入的、带有自我成长和心理转变色彩的疑问。这表明客户已经从表层的知识获取，进入到对自我限制、深层心理模式的探索阶段。此类问题往往涉及到价值观、信念、过往经验等复杂因素，超出了单一课程内容的战术解答范畴，需要战略层面重新规划客户成长路径和干预重点。
</reasoning>

<isPlannerTrigger>
true
</isPlannerTrigger>`],

      ['user', `
当前客户消息：：你知道打动我的从来不是唐宁老师，而是你的每一句话`],

      ['assistant', `<reasoning>
客户表达了对助教的信任和认可，这是情感连接的体现。虽然这是积极的反馈，但这属于现有"建立信任"策略的成功体现，是预期中的互动结果。客户没有提供新的个人画像信息，也没有暴露深层问题或外部阻力。
</reasoning>

<isPlannerTrigger>
false
</isPlannerTrigger>`],

      ['user', `对话历史：
assistant:: ✨【课前必做】...一定提前看！： https://...

当前客户消息：好`],

      ['assistant', `<reasoning>
客户只是简单回复"好"，表示收到信息的确认。这是标准的互动流程，没有提供任何新的关键信息，也没有暴露深层需求或成交风险，属于正常的沟通确认。
</reasoning>

<isPlannerTrigger>
false
</isPlannerTrigger>`],

      ['user', `对话历史：
assistant: 您看完第一节课跟我说说感受哈

当前客户消息：跟练后，心情好点了，但是容易跑毛`],

      ['assistant', `<reasoning>
客户提供了课后练习的反馈，包括积极的情绪变化和遇到的小问题（容易跑毛）。这是预期中的课后反馈，反映了练习效果和需要改进的地方，但没有涉及深层心理问题或重大障碍。
</reasoning>

<isPlannerTrigger>
false
</isPlannerTrigger>`],

      ['user', `
当前客户消息：八点`],

      ['assistant', `<reasoning>
客户简单回复"八点"，可能是在确认时间。这是一个简短的信息确认，没有包含任何关键个人信息、心理需求、外部阻力或复杂情况，属于日常沟通中的时间确认。
</reasoning>

<isPlannerTrigger>
false
</isPlannerTrigger>`],

      // 实际输入
      ['user', `对话历史：{conversationHistory}
      
客户画像：{userProfile}      

当前客户消息：{currentUserMessage}`]
    ])
  }

  /**
   * 解析 XML 格式的响应
   */
  private parseXmlResponse(response: string): PlannerTriggerOutput {
    try {
      const reasoning = XMLHelper.extractContent(response, 'reasoning') || ''
      const isPlannerTriggerStr = XMLHelper.extractContent(response, 'isPlannerTrigger') || 'false'

      const isPlannerTrigger = isPlannerTriggerStr.toLowerCase().trim() === 'true'

      return {
        reasoning: reasoning.trim(),
        isPlannerTrigger
      }
    } catch (error) {
      logger.error('Failed to parse XML response:', error)
      return {
        reasoning: '解析响应失败',
        isPlannerTrigger: false
      }
    }
  }

  /**
   * 静态方法：快速检测
   * @param conversationHistory 对话历史
   * @param currentUserMessage 当前客户消息
   * @param userProfile
   * @param model LLM 模型
   * @returns 检测结果
   */
  public static async quickDetect(
    conversationHistory: string,
    currentUserMessage: string,
    userProfile: string,
    model?: string,
  ): Promise<PlannerTriggerOutput> {
    const detector = new PlannerTrigger({ model })
    return detector.detectTrigger({ conversationHistory, currentUserMessage, userProfile })
  }
}
