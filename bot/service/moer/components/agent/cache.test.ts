import { CacheTestHelper } from './cache_test_helper'
import { CourseCompletionCache } from './course_completion_cache'
import { PromptBuilder } from './context'
it('testBasicCacheEffect', async () => {
  const testChatId = '7881302184908792_1688858047620029' // 替换为实际的chat_id

  console.log('🚀 开始基础缓存效果测试')
  await CacheTestHelper.testCacheEffectiveness(
    async (chatId: string) => {
      return await PromptBuilder.getCustomerBehavior(chatId)
    },
    testChatId,
    '基础缓存效果测试'
  )
}, 60000)