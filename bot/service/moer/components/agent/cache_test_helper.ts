/**
 * 缓存测试辅助工具
 * 用于监控和测试缓存效果
 */

interface APICallStats {
  getUserCourses: number
  getUserChapterStatus: number
  getViewLog: number
  isCompletedCourse: number
  total: number
}

export class CacheTestHelper {
  private static stats: APICallStats = {
    getUserCourses: 0,
    getUserChapterStatus: 0,
    getViewLog: 0,
    isCompletedCourse: 0,
    total: 0
  }

  /**
   * 重置统计计数器
   */
  public static resetStats(): void {
    this.stats = {
      getUserCourses: 0,
      getUserChapterStatus: 0,
      getViewLog: 0,
      isCompletedCourse: 0,
      total: 0
    }
  }

  /**
   * 记录API调用
   */
  public static recordAPICall(apiName: keyof Omit<APICallStats, 'total'>): void {
    this.stats[apiName]++
    this.stats.total++
    console.log(`[API调用] ${apiName} - 总计: ${this.stats.total}`)
  }

  /**
   * 获取当前统计信息
   */
  public static getStats(): APICallStats {
    return { ...this.stats }
  }

  /**
   * 打印统计信息
   */
  public static printStats(label: string): void {
    console.log(`\n=== ${label} ===`)
    console.log(`getUserCourses: ${this.stats.getUserCourses}`)
    console.log(`getUserChapterStatus: ${this.stats.getUserChapterStatus}`)
    console.log(`getViewLog: ${this.stats.getViewLog}`)
    console.log(`isCompletedCourse: ${this.stats.isCompletedCourse}`)
    console.log(`总API调用次数: ${this.stats.total}`)
    console.log('========================\n')
  }

  /**
   * 测试缓存效果
   */
  public static async testCacheEffectiveness(
    testFunction: (chatId: string) => Promise<string>,
    chatId: string,
    testName: string = '缓存效果测试'
  ): Promise<void> {
    console.log(`\n🧪 开始 ${testName}`)
    
    // 清除缓存确保测试的准确性
    const { CourseCompletionCache } = await import('./course_completion_cache')
    CourseCompletionCache.clearCache(chatId)
    
    // 第一次调用
    console.log('\n📞 第一次调用（预期会调用API）...')
    this.resetStats()
    const result1 = await testFunction(chatId)
    const stats1 = this.getStats()
    this.printStats('第一次调用统计')
    
    // 等待一小段时间确保时间戳不同
    await new Promise(resolve => setTimeout(resolve, 100))
    
    // 第二次调用
    console.log('📞 第二次调用（预期使用缓存）...')
    this.resetStats()
    const result2 = await testFunction(chatId)
    const stats2 = this.getStats()
    this.printStats('第二次调用统计')
    
    // 分析结果
    console.log('📊 缓存效果分析:')
    console.log(`第一次API调用总数: ${stats1.total}`)
    console.log(`第二次API调用总数: ${stats2.total}`)
    console.log(`API调用减少: ${stats1.total - stats2.total} (${((1 - stats2.total / Math.max(stats1.total, 1)) * 100).toFixed(1)}%)`)
    
    // 验证结果一致性
    if (result1 === result2) {
      console.log('✅ 两次调用结果一致')
    } else {
      console.log('❌ 两次调用结果不一致')
      console.log('第一次结果:', result1)
      console.log('第二次结果:', result2)
    }
    
    // 缓存统计
    const cacheStats = CourseCompletionCache.getCacheStats()
    console.log(`📈 缓存统计: ${cacheStats.size}/${cacheStats.maxSize}`)
    
    console.log(`\n✅ ${testName} 完成\n`)
  }

  /**
   * 测试不同时间间隔的缓存效果
   */
  public static async testCacheWithTimeIntervals(
    testFunction: (chatId: string) => Promise<string>,
    chatId: string,
    intervals: number[] = [0, 1000, 5000, 10000] // 毫秒
  ): Promise<void> {
    console.log('\n🕐 测试不同时间间隔的缓存效果')
    
    const { CourseCompletionCache } = await import('./course_completion_cache')
    CourseCompletionCache.clearCache(chatId)
    
    for (let i = 0; i < intervals.length; i++) {
      const interval = intervals[i]
      
      if (i > 0) {
        console.log(`⏳ 等待 ${interval}ms...`)
        await new Promise(resolve => setTimeout(resolve, interval))
      }
      
      console.log(`\n📞 第${i + 1}次调用 (间隔: ${interval}ms)`)
      this.resetStats()
      await testFunction(chatId)
      const stats = this.getStats()
      console.log(`API调用次数: ${stats.total}`)
    }
  }

  /**
   * 压力测试 - 连续多次调用
   */
  public static async stressTest(
    testFunction: (chatId: string) => Promise<string>,
    chatId: string,
    callCount: number = 10
  ): Promise<void> {
    console.log(`\n💪 压力测试 - 连续${callCount}次调用`)
    
    const { CourseCompletionCache } = await import('./course_completion_cache')
    CourseCompletionCache.clearCache(chatId)
    
    const results: number[] = []
    
    for (let i = 0; i < callCount; i++) {
      this.resetStats()
      await testFunction(chatId)
      const stats = this.getStats()
      results.push(stats.total)
      console.log(`第${i + 1}次调用: ${stats.total} API调用`)
    }
    
    console.log('\n📊 压力测试结果:')
    console.log(`首次调用API数: ${results[0]}`)
    console.log(`后续调用API数: ${results.slice(1).join(', ')}`)
    console.log(`平均API调用数: ${(results.reduce((a, b) => a + b, 0) / results.length).toFixed(2)}`)
    console.log(`缓存命中率: ${((1 - results.slice(1).reduce((a, b) => a + b, 0) / Math.max(results[0] * (results.length - 1), 1)) * 100).toFixed(1)}%`)
  }
}
