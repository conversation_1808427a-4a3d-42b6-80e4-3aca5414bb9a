import logger from '../../../../model/logger/logger'
import { MoerWorkFlowNode, trackInvoke } from '../flow/nodes/baseNode'
import { IWorkflowState } from '../flow/flow'
import { MoerNode } from '../flow/nodes/type'
import { LLMNode } from '../flow/nodes/llm'
import { ChatStateStore } from '../../storage/chat_state_store'
import { FreeThink } from './freethink'
import { PostSaleNode } from '../flow/nodes/postSaleNode'
import { MetaActionRouter } from '../meta_action/meta_action_router'
import { Planner } from '../planner'
import { SalesNodeHelper } from '../flow/helper/salesNodeHelper'
import { SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { LLM } from '../../../../lib/ai/llm/LLM'
import { TaskStatus } from '@prisma/client'
import { TaskManager } from '../planner/task/task_manager'

export class FreeTalk extends MoerWorkFlowNode {

  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    if (ChatStateStore.get(state.chat_id).state.is_complete_payment && !ChatStateStore.get(state.chat_id).state.is_complete_post_sale) {
      return await PostSaleNode.invoke(state)
    }
    // 针对外部测试人员的降智版本
    const test_chat_ids = ['7881299599245568_1688855548631328']
    let action: string[]
    let strategy: string

    // 检查并创建规划
    await Planner.checkAndCreatePlan(state)

    const metaActionStage = await MetaActionRouter.getThinkAndMetaActions(state.chat_id, state.round_id)

    if (test_chat_ids.includes(state.chat_id)) {
      action = []
      strategy = '跟客户开玩笑，完全无下限'
    } else {
      const result = await FreeThink.invoke(state, metaActionStage)
      action = result.action
      strategy = result.strategy
    }

    const activeTasks = await TaskManager.getStringifyActiveTasks(state.chat_id)
    const actionInfo = await MetaActionRouter.handleAction(state.chat_id, state.round_id, action)
    const talkStrategyPrompt = [
      '无论客户最后说什么，参考上述信息回答完客户后，都要严格执行下面的策略，要求极度精简，点到为止',
      strategy,
      metaActionStage.guidance,
      actionInfo.guidance
    ].filter(Boolean).join('\n')

    await LLMNode.invoke({
      state,
      model: test_chat_ids.includes(state.chat_id) ? 'gpt-4.1-mini' : 'gpt-4.1',
      useRAG: true,
      recallMemory: true,
      chatHistoryRounds: 6,
      promptName: 'free_talk',
      noStagePrompt: true,
      dynamicPrompt: talkStrategyPrompt,
      postReplyCallBack: actionInfo.callback
    })

    // 检查规划完成情况并更新
    if (activeTasks) {
      const dialogHistory = await SalesNodeHelper.getChatHistory(state.chat_id, 6, 18)
      const tasks = await TaskManager.getActiveTasks(state.chat_id)
      const tasksToSchedule = tasks.map((task) => {
        return {
          task_id: task.id.slice(-4),
          task_description: task.description
        }
      })

      const taskCheckPrompt = SystemMessagePromptTemplate.fromTemplate(`# 规划检查
- 你是一个规划完成情况检查助手

# 主要任务
- 思考（think）请根据对话历史判断当前规划是否完成，即使实际内容没有完成，只要完成语义传递就是完成。
- 答案（answer）仅输出完成的任务ID数组

# 对话历史
{{dialogHistory}}

# 当前规划
${tasksToSchedule.map((t) => `规划ID：${t.task_id}，描述：${t.task_description}`).join('\n')}

# 输出要求
请严格按照如下 JSON 格式输出
{
  "think": "（深度思考内容）",
  "answer": ["0001", "0002"]（没有就输出空数组）
}`, { templateFormat: 'mustache' })

      const output = await LLM.predict(
        taskCheckPrompt, {
          response_json: true,
          meta: { promptName: 'check_completed_tasks', chat_id: state.chat_id, round_id: state.round_id } },
        { dialogHistory: dialogHistory })

      let answer: string[] = []

      try {
        const parsedOutput = JSON.parse(output)
        answer = parsedOutput.answer
      } catch (error) {
        logger.error('check_completed_tasks 解析 JSON 失败:', error)
      }

      if (answer.length > 0) {
        logger.log({ chat_id: state.chat_id }, `完成任务: ${answer.join(', ')}`)

        // 对 task Id 进行还原
        const idMap = tasks.reduce((map, task) => {
          map[task.id.slice(-4)] = task.id
          return map
        }, {} as Record<string, string>)

        for (const id of answer) {
          await TaskManager.updateStatus(idMap[id], TaskStatus.DONE)
        }
      }
    }
    return MoerNode.FreeTalk
  }
}