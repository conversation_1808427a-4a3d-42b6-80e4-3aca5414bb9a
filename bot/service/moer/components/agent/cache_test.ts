import { PromptBuilder } from './context'
import { CacheTestHelper } from './cache_test_helper'
import { CourseCompletionCache } from './course_completion_cache'

/**
 * 缓存效果测试脚本
 *
 * 使用方法：
 * 1. 确保有一个有效的 chat_id
 * 2. 运行测试函数
 * 3. 观察API调用次数的变化
 */

async function testBasicCacheEffect() {
  const testChatId = '7881302184908792_1688858047620029' // 替换为实际的chat_id

  console.log('🚀 开始基础缓存效果测试')

  await CacheTestHelper.testCacheEffectiveness(
    async (chatId: string) => {
      return await PromptBuilder.getCustomerBehavior(chatId)
    },
    testChatId,
    '基础缓存效果测试'
  )
}

async function testTimeIntervalEffect() {
  const testChatId = 'test_chat_12345' // 替换为实际的chat_id

  console.log('🚀 开始时间间隔测试')

  await CacheTestHelper.testCacheWithTimeIntervals(
    async (chatId: string) => {
      return await PromptBuilder.getCustomerBehavior(chatId)
    },
    testChatId,
    [0, 1000, 30000, 60000, 300000] // 0秒, 1秒, 30秒, 1分钟, 5分钟
  )
}

async function testStressEffect() {
  const testChatId = 'test_chat_12345' // 替换为实际的chat_id

  console.log('🚀 开始压力测试')

  await CacheTestHelper.stressTest(
    async (chatId: string) => {
      return await PromptBuilder.getCustomerBehavior(chatId)
    },
    testChatId,
    20 // 连续20次调用
  )
}

async function testMultipleUsers() {
  const testChatIds = [
    'test_chat_user1',
    'test_chat_user2',
    'test_chat_user3'
  ]

  console.log('🚀 开始多用户测试')

  for (const chatId of testChatIds) {
    console.log(`\n👤 测试用户: ${chatId}`)

    await CacheTestHelper.testCacheEffectiveness(
      async (chatId: string) => {
        return await PromptBuilder.getCustomerBehavior(chatId)
      },
      chatId,
      `用户${chatId}的缓存测试`
    )
  }

  // 测试缓存统计
  const cacheStats = CourseCompletionCache.getCacheStats()
  console.log(`\n📊 最终缓存统计: ${cacheStats.size}/${cacheStats.maxSize}`)
}

async function testCacheClearEffect() {
  const testChatId = 'test_chat_12345'

  console.log('🚀 开始缓存清除效果测试')

  // 第一次调用建立缓存
  console.log('\n📞 第一次调用（建立缓存）')
  CacheTestHelper.resetStats()
  await PromptBuilder.getCustomerBehavior(testChatId)
  const stats1 = CacheTestHelper.getStats()
  CacheTestHelper.printStats('建立缓存')

  // 第二次调用使用缓存
  console.log('📞 第二次调用（使用缓存）')
  CacheTestHelper.resetStats()
  await PromptBuilder.getCustomerBehavior(testChatId)
  const stats2 = CacheTestHelper.getStats()
  CacheTestHelper.printStats('使用缓存')

  // 清除缓存
  console.log('🗑️ 清除缓存')
  CourseCompletionCache.clearCache(testChatId)

  // 第三次调用重新建立缓存
  console.log('📞 第三次调用（重新建立缓存）')
  CacheTestHelper.resetStats()
  await PromptBuilder.getCustomerBehavior(testChatId)
  const stats3 = CacheTestHelper.getStats()
  CacheTestHelper.printStats('重新建立缓存')

  console.log('📊 缓存清除效果分析:')
  console.log(`建立缓存API调用: ${stats1.total}`)
  console.log(`使用缓存API调用: ${stats2.total}`)
  console.log(`重新建立缓存API调用: ${stats3.total}`)
  console.log(`缓存清除后API调用是否恢复: ${stats3.total >= stats1.total ? '✅ 是' : '❌ 否'}`)
}

// 主测试函数
async function runAllTests() {
  try {
    console.log('🎯 开始完整的缓存效果测试套件\n')

    // 基础测试
    await testBasicCacheEffect()

    // 时间间隔测试
    await testTimeIntervalEffect()

    // 压力测试
    await testStressEffect()

    // 多用户测试
    await testMultipleUsers()

    // 缓存清除测试
    await testCacheClearEffect()

    console.log('🎉 所有测试完成！')

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error)
  }
}

// 导出测试函数
export {
  testBasicCacheEffect,
  testTimeIntervalEffect,
  testStressEffect,
  testMultipleUsers,
  testCacheClearEffect,
  runAllTests
}

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  runAllTests()
}
