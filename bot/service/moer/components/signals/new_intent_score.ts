import { DataService } from '../../getter/getData'
import { ChatHistoryService } from '../chat_history/chat_history'
import { ChatStateStore } from '../../storage/chat_state_store'
import { DateHelper } from '../../../../lib/date/date'
import logger from '../../../../model/logger/logger'
import { LLM } from '../../../../lib/ai/llm/LLM'
import { SystemMessagePromptTemplate } from '@langchain/core/prompts'

interface NewIntentScoreResult {
  stage: 'pre_course' | 'day1' | 'day2' | 'day3' | 'day4'
  intent_score: number

  // 进量期意向判断数据
  pre_course_data?: {
    has_conversation: boolean
    completed_leading_course: boolean
    completed_need_mining: boolean
    completed_energy_test: boolean
    conversation_rounds: number
  }

  // 上课期数据
  course_data?: {
    day: number
    conversation_rounds: number
    course_watch_minutes: number
    replied_after_course_feeling: boolean
    has_purchased?: boolean
  }
}

class NewIntentCalculator {

  /**
   * 计算客户意向度（新版本逻辑）
   * @param chatId 客户聊天ID
   * @returns 意向度计算结果
   */
  public static async calculateIntentScore(chatId: string): Promise<NewIntentScoreResult> {
    try {
      // 判断客户当前是在进量期还是上课期
      const currentTime = await DataService.getCurrentTime(chatId)

      if (!currentTime.is_course_week) {
        return await this.calculatePreCourseIntentScore(chatId)
      } else {
        return await this.calculateCourseIntentScore(chatId, currentTime.day)
      }
    } catch (error) {
      logger.error('计算意向度失败', error)
      throw error
    }
  }

  /**
   * 计算进量期意向度（新版本逻辑）
   * @param chatId 客户聊天ID
   * @returns 进量期意向度结果
   */
  public static async calculatePreCourseIntentScore(chatId: string): Promise<NewIntentScoreResult> {
    // 获取课程开始时间
    const courseStartTime = await DataService.getCourseStartTime(chatId)

    // 获取接量期的对话轮数
    const conversationRounds = await this.getPreCourseConversationRounds(chatId, courseStartTime)

    // 1. 对话中表示过目前自己的困扰（使用LLM分析）
    const hasExpressedTroubles = await this.checkHasExpressedTroubles(chatId)

    // 2. 先导课看过程度（看过+10，看完再+10）
    const leadingCourseScore = await this.calculateLeadingCourseScore(chatId)

    // 3. 挖需：是否回复过formIntentionQuery中的'您目前的生活角色...'
    const repliedIntentionQuery = await this.checkRepliedIntentionQuery(chatId)

    // 4. 能量测评
    const chatState = ChatStateStore.getFlags(chatId)
    const completedEnergyTest = Boolean(chatState.is_complete_energy_test || chatState.is_complete_energy_test_analyze)

    // 5. 是否进群
    const hasJoinedGroup = await this.checkIsInGroup(chatId)

    // 6. 对话轮数>5
    const hasEnoughConversation = conversationRounds > 5

    // 新版本计分规则：每满足一项+10分
    let intentScore = 0
    if (conversationRounds > 0) intentScore += 10
    if (hasExpressedTroubles) intentScore += 10
    intentScore += leadingCourseScore // 0, 10, 或 20分
    if (repliedIntentionQuery) intentScore += 10
    if (completedEnergyTest) intentScore += 10
    if (hasJoinedGroup) intentScore += 10
    if (hasEnoughConversation) intentScore += 10


    return {
      stage: 'pre_course',
      intent_score: intentScore,
      pre_course_data: {
        has_conversation: hasEnoughConversation,
        completed_leading_course: leadingCourseScore > 0,
        completed_need_mining: repliedIntentionQuery,
        completed_energy_test: completedEnergyTest,
        conversation_rounds: conversationRounds
      }
    }
  }

  /**
   * 计算上课期意向度（新版本逻辑）
   * @param chatId 客户聊天ID
   * @param currentDay 当前天数（1-4）
   * @returns 上课期意向度结果
   */
  public static async calculateCourseIntentScore(chatId: string, currentDay: number): Promise<NewIntentScoreResult> {
    if (currentDay < 1 || currentDay > 4) {
      throw new Error('当前天数必须在1-4之间')
    }

    // 使用新的计算逻辑
    if (currentDay === 1) {
      return await this.calculateDay1IntentScore(chatId)
    } else if (currentDay === 2) {
      return await this.calculateDay2IntentScore(chatId)
    } else if (currentDay === 3) {
      return await this.calculateDay3IntentScore(chatId)
    } else if (currentDay === 4) {
      return await this.calculateDay4IntentScore(chatId)
    }

    // 其他天数使用原有逻辑
    const conversationRounds = await this.getDayConversationRounds(chatId, currentDay)
    const courseWatchMinutes = await this.getCourseWatchMinutes(chatId, currentDay)
    const repliedAfterCourseFeeling = await this.hasRepliedAfterCourseFeeling(chatId, currentDay)

    const intentScore = this.calculateCourseIntentScoreValue(
      conversationRounds,
      courseWatchMinutes,
      repliedAfterCourseFeeling
    )


    return {
      stage: `day${currentDay}` as 'day1' | 'day2' | 'day3' | 'day4',
      intent_score: intentScore,
      course_data: {
        day: currentDay,
        conversation_rounds: conversationRounds,
        course_watch_minutes: courseWatchMinutes,
        replied_after_course_feeling: repliedAfterCourseFeeling
      }
    }
  }

  /**
   * 获取指定天的对话轮数
   * @param chatId 客户聊天ID
   * @param day 天数（1-4）
   * @returns 对话轮数
   */
  private static async getDayConversationRounds(chatId: string, day: number): Promise<number> {
    try {
      // 获取课程开始时间
      const courseStartTime = await DataService.getCourseStartTime(chatId)

      // 计算指定天的开始和结束时间
      const dayStartTime = new Date(courseStartTime)
      dayStartTime.setDate(dayStartTime.getDate() + day - 1)
      dayStartTime.setHours(0, 0, 0, 0)

      const dayEndTime = new Date(dayStartTime)
      dayEndTime.setHours(23, 59, 59, 999)

      // 获取该时间段内的聊天历史
      const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)

      // 筛选指定时间段内的用户消息
      const dayMessages = chatHistory.filter((message) => {
        const messageTime = new Date(message.created_at)
        return message.role === 'user' &&
               messageTime >= dayStartTime &&
               messageTime <= dayEndTime
      })

      return dayMessages.length
    } catch (error) {
      logger.error(`获取Day${day}对话轮数失败`, error)
      return 0
    }
  }

  /**
   * 获取课程观看分钟数
   * @param chatId 客户聊天ID
   * @param day 天数（1-4）
   * @returns 观看分钟数
   */
  private static async getCourseWatchMinutes(chatId: string, day: number): Promise<number> {
    try {
      // 取moerId
      const moerId = await DataService.getMoerIdByChatId(chatId)
      if (!moerId) {
        return 0
      }

      // 取课程信息
      const courseMap = await DataService.getMoerCourses(moerId)

      // 优先获取直播课程状态，如果没有则获取录播课程状态
      let courseStatus
      if (courseMap[day] && courseMap[day].live) {
        courseStatus = await DataService.getMoerCourseStatus(courseMap[day].live!)
      } else if (courseMap[day] && courseMap[day].record) {
        courseStatus = await DataService.getMoerCourseStatus(courseMap[day].record!)
      } else {
        return 0
      }

      if (!courseStatus || !courseStatus.duration) {
        return 0
      }

      let playbackTime = courseStatus.playbackTime
      if (typeof playbackTime === 'string') {
        playbackTime = Number(playbackTime)
      }

      // 转换为分钟数
      const watchMinutes = Math.floor(playbackTime / 60)
      return Math.max(0, watchMinutes)
    } catch (error) {
      logger.error(`获取Day${day}课程观看分钟数失败`, error)
      return 0
    }
  }

  /**
   * 检查是否回复了课后感悟
   * @param chatId 客户聊天ID
   * @param day 天数（1-4）
   * @returns 是否回复了课后感悟
   */
  private static async hasRepliedAfterCourseFeeling(chatId: string, day: number): Promise<boolean> {
    try {
      const chatState = ChatStateStore.get(chatId).state

      // 根据天数检查对应的课后感悟完成状态
      const dayStatusMap = {
        1: 'is_complete_day1_homework_feedback',
        2: 'is_complete_day2_homework_feedback',
        3: 'is_complete_day3_homework_feedback',
        4: 'is_complete_day4_homework_feedback'
      }

      const status = dayStatusMap[day as keyof typeof dayStatusMap]
      return Boolean(chatState[status])
    } catch (error) {
      logger.error(`检查Day${day}课后感悟失败`, error)
      return false
    }
  }

  /**
   * 计算上课期意向度分数（新版本逻辑）
   * @param conversationRounds 对话轮数
   * @param courseWatchMinutes 课程观看分钟数
   * @param repliedAfterCourseFeeling 是否回复课后感悟
   * @returns 意向度分数
   */
  private static calculateCourseIntentScoreValue(
    conversationRounds: number,
    courseWatchMinutes: number,
    repliedAfterCourseFeeling: boolean
  ): number {
    // 对话轮数分数：每轮对话3分，最高30分
    const conversationScore = Math.min(conversationRounds * 3, 30)

    // 课程观看分钟数分数：每分钟0.5分，最高50分
    const watchScore = Math.min(courseWatchMinutes * 0.5, 50)

    // 课后感悟分数：回复了得20分
    const feelingScore = repliedAfterCourseFeeling ? 20 : 0

    return conversationScore + watchScore + feelingScore
  }

  /**
   * 批量计算指定期数范围内所有客户的意向度
   * @param startCourseNo 开始期数
   * @param endCourseNo 结束期数
   * @returns 客户意向度数据数组
   */
  public static async calculateBatchIntentScores(
    startCourseNo: number,
    endCourseNo: number
  ): Promise<Array<{
    chatId: string
    customerName: string
    courseNo: number
    preCourseScore: number
    day1Score: number
    day2Score: number
    day3Score: number
    day4Score: number
  }>> {
    const results: Array<{
      chatId: string
      customerName: string
      courseNo: number
      preCourseScore: number
      day1Score: number
      day2Score: number
      day3Score: number
      day4Score: number
    }> = []

    // 获取指定期数范围内的所有客户
    for (let courseNo = startCourseNo; courseNo <= endCourseNo; courseNo++) {
      const chats = await DataService.getChatsByCourseNo(courseNo)

      for (const chat of chats) {
        try {
          // 计算进量期意向度
          const preCourseResult = await this.calculatePreCourseIntentScore(chat.id)

          // 计算各天意向度
          const day1Result = await this.calculateCourseIntentScore(chat.id, 1)
          const day2Result = await this.calculateCourseIntentScore(chat.id, 2)
          const day3Result = await this.calculateCourseIntentScore(chat.id, 3)
          const day4Result = await this.calculateCourseIntentScore(chat.id, 4)

          results.push({
            chatId: chat.id,
            customerName: chat.contact.wx_name,
            courseNo: courseNo,
            preCourseScore: preCourseResult.intent_score,
            day1Score: day1Result.intent_score,
            day2Score: day2Result.intent_score,
            day3Score: day3Result.intent_score,
            day4Score: day4Result.intent_score
          })
        } catch (error) {
          logger.error(`计算客户${chat.id}意向度失败`, error)
          // 继续处理下一个客户
        }
      }
    }

    return results
  }

  /**
   * 获取接量期的对话轮数
   * @param chatId 客户聊天ID
   * @param courseStartTime 课程开始时间
   * @returns 接量期对话轮数
   */
  private static async getPreCourseConversationRounds(chatId: string, courseStartTime: Date): Promise<number> {
    try {
      const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)

      // 只统计接量期（课程开始前）的用户消息
      const preCourseUserMessages = chatHistory.filter((msg) => {
        const messageTime = new Date(msg.created_at)
        return msg.role === 'user' && messageTime < courseStartTime
      })

      return preCourseUserMessages.length
    } catch (error) {
      logger.error('获取接量期对话轮数失败', error)
      return 0
    }
  }

  /**
   * 检查客户是否在对话中表达过困扰（使用LLM分析）
   * @param chatId 客户聊天ID
   * @returns 是否表达过困扰
   */
  private static async checkHasExpressedTroubles(chatId: string): Promise<boolean> {
    try {
      // 获取课程开始时间
      const courseStartTime = await DataService.getCourseStartTime(chatId)

      // 获取客户的聊天历史
      const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)

      // 只分析接量期（课程开始前）的用户消息
      const preCourseUserMessages = chatHistory
        .filter((msg) => {
          const messageTime = new Date(msg.created_at)
          return msg.role === 'user' && messageTime < courseStartTime
        })
        .slice(-20) // 限制在最近20条
        .map((msg) => msg.content)
        .join('\n')

      if (!preCourseUserMessages.trim()) {
        return false
      }

      const prompt = SystemMessagePromptTemplate.fromTemplate(`请分析以下客户的聊天记录，判断客户是否在对话中表达过自己目前的困扰、问题或痛点。

客户聊天记录：
{chat_history}

判断标准：
- 客户提到了生活中的具体困难、压力或问题
- 客户表达了情绪上的困扰（如焦虑、压力、失眠等）
- 客户描述了工作、家庭、人际关系等方面的挑战
- 客户提到了希望改善的现状

请仅回答 "true" 或 "false"。`)

      const response = await LLM.predict(prompt, {
        model: 'gpt-4.1-mini',
        temperature: 0,
        meta: { chat_id: chatId }
      }, { chat_history: preCourseUserMessages })

      return response.trim().toLowerCase() === 'true'
    } catch (error) {
      logger.error('检查客户困扰表达失败', error)
      return false
    }
  }

  /**
   * 计算先导课观看分数
   * @param chatId 客户聊天ID
   * @returns 先导课分数（0, 10, 或 20）
   */
  private static async calculateLeadingCourseScore(chatId: string): Promise<number> {
    try {
      // 检查是否看过先导课（任何观看时长）
      const hasWatched = await DataService.isInClass(chatId, { day: 0 })

      if (!hasWatched) {
        return 0
      }

      // 检查是否完课（原有逻辑）
      const hasCompleted = await DataService.isCompletedCourse(chatId, { day: 0 })

      if (hasCompleted) {
        return 20 // 看过+看完
      } else {
        return 10 // 只是看过
      }
    } catch (error) {
      logger.error('计算先导课分数失败', error)
      return 0
    }
  }

  /**
   * 检查客户是否回复过意向调研问卷
   * @param chatId 客户聊天ID
   * @returns 是否回复过问卷
   */
  private static async checkRepliedIntentionQuery(chatId: string): Promise<boolean> {
    try {
      // 获取课程开始时间
      const courseStartTime = await DataService.getCourseStartTime(chatId)

      // 获取聊天历史
      const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)

      // 只分析接量期（课程开始前）的聊天记录
      const preCourseHistory = chatHistory.filter((msg) => {
        const messageTime = new Date(msg.created_at)
        return messageTime < courseStartTime
      })

      // 查找是否有发送过意向调研的消息
      const hasIntentionQuerySent = preCourseHistory.some((msg) =>
        msg.role === 'assistant' &&
        msg.content.includes('您目前的生活角色')
      )

      if (!hasIntentionQuerySent) {
        return false
      }

      // 查找发送意向调研后是否有用户回复
      let foundQuery = false
      for (const msg of preCourseHistory) {
        if (msg.role === 'assistant' && msg.content.includes('您目前的生活角色')) {
          foundQuery = true
          continue
        }

        if (foundQuery && msg.role === 'user') {
          // 检查用户回复是否包含数字（问卷选项）
          const hasNumbers = /[1-9]/.test(msg.content)
          if (hasNumbers) {
            return true
          }
        }
      }

      return false
    } catch (error) {
      logger.error('检查意向调研回复失败', error)
      return false
    }
  }

  /**
   * 计算第一天意向度（新版本逻辑）
   * @param chatId 客户聊天ID
   * @returns 第一天意向度结果
   */
  private static async calculateDay1IntentScore(chatId: string): Promise<NewIntentScoreResult> {
    try {
      // 获取第一天的基础数据
      const day1Data = await this.getDay1Data(chatId)

      let intentScore = 0

      // 1. 当天开课前对话轮次和接星期的平均对比
      if (day1Data.preCourseConversationRatio > 1) {
        intentScore += 10 // 减少不满意，增加+10分
      }

      // 2. 当天完全没回复过 -10分
      if (day1Data.totalReplies === 0) {
        intentScore -= 10
      }

      // 3. 截至目前为止从未对话过 -10分
      if (day1Data.neverReplied) {
        intentScore -= 10
      }

      // 4. 回复参加但未参加 -5分
      if (day1Data.repliedJoinButNotJoin) {
        intentScore -= 5
      }

      // 5. 虽然没参加但给出了参加原因 +5分
      if (day1Data.gaveReasonForNotJoining) {
        intentScore += 5
      }

      // 6. 看课进度
      if (day1Data.courseWatchMinutes >= 10) {
        intentScore += 10 // 到课+10分
      }
      if (day1Data.courseCompleted) {
        intentScore += 10 // 完课+10分
      }

      // 7. 上课打卡感受（只要打卡）+10分
      if (day1Data.hasCheckedIn) {
        intentScore += 10
      }

      // 8. 打卡感受评分
      const checkInScore = this.calculateCheckInScore(day1Data.checkInSentiment)
      intentScore += checkInScore

      return {
        stage: 'day1',
        intent_score: intentScore,
        course_data: {
          day: 1,
          conversation_rounds: day1Data.conversationRounds,
          course_watch_minutes: day1Data.courseWatchMinutes,
          replied_after_course_feeling: day1Data.hasCheckedIn
        }
      }
    } catch (error) {
      logger.error('计算第一天意向度失败', error)
      throw error
    }
  }

  /**
   * 计算第二天意向度（新版本逻辑）
   * @param chatId 客户聊天ID
   * @returns 第二天意向度结果
   */
  private static async calculateDay2IntentScore(chatId: string): Promise<NewIntentScoreResult> {
    try {
      // 获取第二天的基础数据
      const day2Data = await this.getDay2Data(chatId)

      let intentScore = 0

      // 1. 当天开课前对话轮次和接量期的平均对比
      if (day2Data.preCourseConversationRatio > 1) {
        intentScore += 10 // 增加+10分
      } else {
        intentScore -= 10 // 减少-10分
      }

      // 2. 当天完全没回复过 -10分
      if (day2Data.totalReplies === 0) {
        intentScore -= 10
      }

      // 3. 截至目前为止从未对话过 -10分
      if (day2Data.neverReplied) {
        intentScore -= 10
      }

      // 4. 回复参加但未参加 -10分
      if (day2Data.repliedJoinButNotJoin) {
        intentScore -= 10
      }

      // 5. 虽然没参加但给出了原因 +5分
      if (day2Data.gaveReasonForNotJoining) {
        intentScore += 5
      }

      // 6. 看课进度
      if (day2Data.courseWatchMinutes >= 10) {
        intentScore += 10 // 到课+10分
      }
      if (day2Data.courseCompleted) {
        intentScore += 10 // 完课+10分
      }

      // 7. 上课回复感受
      const courseFeelingScore = this.calculateCourseFeelingScore(day2Data.courseFeedback)
      intentScore += courseFeelingScore

      // 8. 说出财富果园画面 +10分
      if (day2Data.mentionedWealthGarden) {
        intentScore += 10
      }

      // 9. 说没有画面 0分 (已包含在默认情况中)

      // 10. 截至目前为止第一&第二节课都没看 -10分
      if (day2Data.neverWatchedDay1And2) {
        intentScore -= 10
      }

      return {
        stage: 'day2',
        intent_score: intentScore,
        course_data: {
          day: 2,
          conversation_rounds: day2Data.conversationRounds,
          course_watch_minutes: day2Data.courseWatchMinutes,
          replied_after_course_feeling: day2Data.hasCourseFeedback
        }
      }
    } catch (error) {
      logger.error('计算第二天意向度失败', error)
      throw error
    }
  }

  /**
   * 计算第三天意向度（新版本逻辑）
   * @param chatId 客户聊天ID
   * @returns 第三天意向度结果
   */
  private static async calculateDay3IntentScore(chatId: string): Promise<NewIntentScoreResult> {
    try {
      // 获取第三天的基础数据
      const day3Data = await this.getDay3Data(chatId)

      let intentScore = 0

      // 1. 当天开课前对话轮次和前两天的平均对比
      if (day3Data.preTwoDaysConversationRatio > 1) {
        intentScore += 10 // 增加+10分
      } else {
        intentScore -= 10 // 减少-10分
      }

      // 2. 当天完全没回复过 -10分
      if (day3Data.totalReplies === 0) {
        intentScore -= 10
      }

      // 3. 截至目前为止从未对话过 -10分
      if (day3Data.neverReplied) {
        intentScore -= 10
      }

      // 4. 回复参加但未参加 -10分
      if (day3Data.repliedJoinButNotJoin) {
        intentScore -= 10
      }

      // 5. 虽然没参加但给出了参加原因 +10分
      if (day3Data.gaveReasonForNotJoining) {
        intentScore += 10
      }

      // 6. 看课进度
      if (day3Data.courseWatchMinutes >= 10) {
        intentScore += 10 // 看课+10分
      }
      if (day3Data.courseCompleted) {
        intentScore += 10 // 看完再+10分
      }

      // 7. 上课感受
      const courseFeelingScore = this.calculateDay3CourseFeelingScore(day3Data.courseFeedback)
      intentScore += courseFeelingScore

      // 8. 对于系统班有问询过 +10分
      if (day3Data.inquiredAboutSystemClass) {
        intentScore += 10
      }

      // 9. 截至目前为止123节课都没看 -20分
      if (day3Data.neverWatchedDay123) {
        intentScore -= 20
      }


      return {
        stage: 'day3',
        intent_score: intentScore,
        course_data: {
          day: 3,
          conversation_rounds: day3Data.conversationRounds,
          course_watch_minutes: day3Data.courseWatchMinutes,
          replied_after_course_feeling: day3Data.hasCourseFeedback
        }
      }
    } catch (error) {
      logger.error('计算第三天意向度失败', error)
      throw error
    }
  }

  /**
   * 计算第四天意向度（新版本逻辑 - 去除下单人群）
   * @param chatId 客户聊天ID
   * @returns 第四天意向度结果
   */
  private static async calculateDay4IntentScore(chatId: string): Promise<NewIntentScoreResult> {
    try {
      // 首先检查是否已下单，如果已下单则不计算意向度
      const hasPurchased = await this.checkHasPurchased(chatId)
      if (hasPurchased) {
        return {
          stage: 'day4',
          intent_score: 100, // 已下单客户给最高分
          course_data: {
            day: 4,
            conversation_rounds: 0,
            course_watch_minutes: 0,
            replied_after_course_feeling: false,
            has_purchased: true
          }
        }
      }

      // 获取第四天的基础数据
      const day4Data = await this.getDay4Data(chatId)

      let intentScore = 0

      // 1. 当天开课前对话轮次和前一天的平均对比
      if (day4Data.preDayConversationRatio > 1) {
        intentScore += 10 // 增加+10分
      } else {
        intentScore -= 10 // 减少-10分
      }

      // 2. 当天回复系统班相关信息信息 +10分
      if (day4Data.repliedSystemClassInfo) {
        intentScore += 10
      }

      // 3. 当天回复过信息 +10分
      if (day4Data.repliedToday) {
        intentScore += 10
      }

      // 4. 到目前为止没有表示过课程不好（没感觉）的感受 +10分
      if (!day4Data.hasNegativeFeedback) {
        intentScore += 10
      }

      // 5. 上课感受
      const courseFeelingScore = this.calculateDay4CourseFeelingScore(day4Data.courseFeedback)
      intentScore += courseFeelingScore

      // 6. 提过补课 +10分
      if (day4Data.mentionedMakeupClass) {
        intentScore += 10
      }

      // 7. 看课进度
      if (day4Data.courseWatchMinutes >= 10) {
        intentScore += 10 // 看课+10分
      }
      if (day4Data.courseCompleted) {
        intentScore += 10 // 看完再+10分
      }

      return {
        stage: 'day4',
        intent_score: intentScore,
        course_data: {
          day: 4,
          conversation_rounds: day4Data.conversationRounds,
          course_watch_minutes: day4Data.courseWatchMinutes,
          replied_after_course_feeling: day4Data.hasCourseFeedback,
          has_purchased: false
        }
      }
    } catch (error) {
      logger.error('计算第四天意向度失败', error)
      throw error
    }
  }

  /**
   * 获取第一天的所有相关数据
   * @param chatId 客户聊天ID
   * @returns 第一天数据
   */
  private static async getDay1Data(chatId: string) {
    const courseStartTime = await DataService.getCourseStartTime(chatId)

    // 计算第一天的时间范围
    const day1StartTime = new Date(courseStartTime)
    const day1EndTime = new Date(courseStartTime)
    day1EndTime.setDate(day1EndTime.getDate() + 1)
    day1EndTime.setHours(0, 0, 0, 0) // 第二天的0点

    // 获取聊天历史
    const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)

    // 第一天的消息
    const day1Messages = chatHistory.filter((msg) => {
      const messageTime = new Date(msg.created_at)
      return messageTime >= day1StartTime && messageTime < day1EndTime
    })

    // 第一天的用户消息
    const day1UserMessages = day1Messages.filter((msg) => msg.role === 'user')

    // 接量期的用户消息（用于对比）
    const preCourseMessages = chatHistory.filter((msg) => {
      const messageTime = new Date(msg.created_at)
      return msg.role === 'user' && messageTime < courseStartTime
    })

    // 计算各项指标
    const conversationRounds = day1UserMessages.length
    const totalReplies = day1UserMessages.length
    const neverReplied = chatHistory.filter((msg) => msg.role === 'user').length === 0

    // 计算开课前对话轮次比例（简化计算）
    const preCourseAverage = preCourseMessages.length / 7 // 假设接量期7天
    const preCourseConversationRatio = preCourseAverage > 0 ? conversationRounds / preCourseAverage : 0

    // 检查是否回复参加但未参加（需要分析消息内容）
    const repliedJoinButNotJoin = await this.checkRepliedJoinButNotJoin(chatId, day1Messages)

    // 检查是否给出了不参加的原因
    const gaveReasonForNotJoining = await this.checkGaveReasonForNotJoining(chatId, day1Messages)

    // 获取课程观看数据
    const courseWatchMinutes = await this.getCourseWatchMinutes(chatId, 1)
    const courseCompleted = await DataService.isCompletedCourse(chatId, { day: 1 })

    // 检查是否有打卡
    const hasCheckedIn = await this.hasRepliedAfterCourseFeeling(chatId, 1)

    // 分析打卡情感
    const checkInSentiment = hasCheckedIn ? await this.analyzeCheckInSentiment(chatId, 1) : 'none'

    return {
      conversationRounds,
      totalReplies,
      neverReplied,
      preCourseConversationRatio,
      repliedJoinButNotJoin,
      gaveReasonForNotJoining,
      courseWatchMinutes,
      courseCompleted,
      hasCheckedIn,
      checkInSentiment
    }
  }

  /**
   * 安全地检查客户是否在群内
   * @param chatId 客户聊天ID
   * @returns 是否在群内
   */
  private static async checkIsInGroup(chatId: string): Promise<boolean> {
    try {
      const moerId = await DataService.getMoerIdByChatId(chatId)
      if (!moerId) {
        return false
      }

      const userId = await DataService.getWxIdByMoerId(moerId)
      if (!userId) {
        return false
      }

      // 检查配置是否完整
      const { Config } = await import('../../../../config/config')
      if (!Config.setting.wechatConfig?.id || !Config.setting.wechatConfig?.classGroupId) {
        logger.warn('微信配置不完整，无法检查进群状态')
        return false
      }

      return await DataService.isInGroup(userId)
    } catch (error) {
      logger.warn('检查进群状态失败', error)
      return false
    }
  }

  /**
   * 获取第二天的所有相关数据
   * @param chatId 客户聊天ID
   * @returns 第二天数据
   */
  private static async getDay2Data(chatId: string) {
    const courseStartTime = await DataService.getCourseStartTime(chatId)

    // 计算第二天的时间范围
    const day2StartTime = new Date(courseStartTime)
    day2StartTime.setDate(day2StartTime.getDate() + 1)
    const day2EndTime = new Date(day2StartTime)
    day2EndTime.setDate(day2EndTime.getDate() + 1)

    // 获取聊天历史
    const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)

    // 第二天的消息
    const day2Messages = chatHistory.filter((msg) => {
      const messageTime = new Date(msg.created_at)
      return messageTime >= day2StartTime && messageTime < day2EndTime
    })

    // 第二天的用户消息
    const day2UserMessages = day2Messages.filter((msg) => msg.role === 'user')

    // 接量期的用户消息（用于对比）
    const preCourseMessages = chatHistory.filter((msg) => {
      const messageTime = new Date(msg.created_at)
      return msg.role === 'user' && messageTime < courseStartTime
    })

    // 计算各项指标
    const conversationRounds = day2UserMessages.length
    const totalReplies = day2UserMessages.length
    const neverReplied = chatHistory.filter((msg) => msg.role === 'user').length === 0

    // 计算开课前对话轮次比例
    const preCourseAverage = preCourseMessages.length / 7 // 假设接量期7天
    const preCourseConversationRatio = preCourseAverage > 0 ? conversationRounds / preCourseAverage : 0

    // 检查各项条件
    const repliedJoinButNotJoin = await this.checkRepliedJoinButNotJoin(chatId, day2Messages)
    const gaveReasonForNotJoining = await this.checkGaveReasonForNotJoining(chatId, day2Messages)

    // 获取课程观看数据
    const courseWatchMinutes = await this.getCourseWatchMinutes(chatId, 2)
    const courseCompleted = await DataService.isCompletedCourse(chatId, { day: 2 })

    // 检查课程反馈
    const hasCourseFeedback = await this.hasRepliedAfterCourseFeeling(chatId, 2)
    const courseFeedback = hasCourseFeedback ? await this.analyzeCourseFeedback(chatId, 2) : 'none'

    // 检查是否提到财富果园画面
    const mentionedWealthGarden = await this.checkMentionedWealthGarden(chatId, day2Messages)

    // 检查是否第一第二节课都没看
    const day1WatchMinutes = await this.getCourseWatchMinutes(chatId, 1)
    const neverWatchedDay1And2 = day1WatchMinutes === 0 && courseWatchMinutes === 0

    return {
      conversationRounds,
      totalReplies,
      neverReplied,
      preCourseConversationRatio,
      repliedJoinButNotJoin,
      gaveReasonForNotJoining,
      courseWatchMinutes,
      courseCompleted,
      hasCourseFeedback,
      courseFeedback,
      mentionedWealthGarden,
      neverWatchedDay1And2
    }
  }

  /**
   * 获取第三天的所有相关数据
   * @param chatId 客户聊天ID
   * @returns 第三天数据
   */
  private static async getDay3Data(chatId: string) {
    const courseStartTime = await DataService.getCourseStartTime(chatId)

    // 计算第三天的时间范围
    const day3StartTime = new Date(courseStartTime)
    day3StartTime.setDate(day3StartTime.getDate() + 2)
    const day3EndTime = new Date(day3StartTime)
    day3EndTime.setDate(day3EndTime.getDate() + 1)

    // 获取聊天历史
    const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)

    // 第三天的消息
    const day3Messages = chatHistory.filter((msg) => {
      const messageTime = new Date(msg.created_at)
      return messageTime >= day3StartTime && messageTime < day3EndTime
    })

    // 第三天的用户消息
    const day3UserMessages = day3Messages.filter((msg) => msg.role === 'user')

    // 前两天的用户消息（用于对比）
    const preTwoDaysMessages = chatHistory.filter((msg) => {
      const messageTime = new Date(msg.created_at)
      return msg.role === 'user' && messageTime >= courseStartTime && messageTime < day3StartTime
    })

    // 计算各项指标
    const conversationRounds = day3UserMessages.length
    const totalReplies = day3UserMessages.length
    const neverReplied = chatHistory.filter((msg) => msg.role === 'user').length === 0

    // 计算前两天对话轮次比例
    const preTwoDaysAverage = preTwoDaysMessages.length / 2 // 前两天平均
    const preTwoDaysConversationRatio = preTwoDaysAverage > 0 ? conversationRounds / preTwoDaysAverage : 0

    // 检查各项条件
    const repliedJoinButNotJoin = await this.checkRepliedJoinButNotJoin(chatId, day3Messages)
    const gaveReasonForNotJoining = await this.checkGaveReasonForNotJoining(chatId, day3Messages)

    // 获取课程观看数据
    const courseWatchMinutes = await this.getCourseWatchMinutes(chatId, 3)
    const courseCompleted = await DataService.isCompletedCourse(chatId, { day: 3 })

    // 检查课程反馈
    const hasCourseFeedback = await this.hasRepliedAfterCourseFeeling(chatId, 3)
    const courseFeedback = hasCourseFeedback ? await this.analyzeCourseFeedback(chatId, 3) : 'none'

    // 检查是否问询过系统班
    const inquiredAboutSystemClass = await this.checkInquiredAboutSystemClass(chatId, day3Messages)

    // 检查是否123节课都没看
    const day1WatchMinutes = await this.getCourseWatchMinutes(chatId, 1)
    const day2WatchMinutes = await this.getCourseWatchMinutes(chatId, 2)
    const neverWatchedDay123 = day1WatchMinutes === 0 && day2WatchMinutes === 0 && courseWatchMinutes === 0

    return {
      conversationRounds,
      totalReplies,
      neverReplied,
      preTwoDaysConversationRatio,
      repliedJoinButNotJoin,
      gaveReasonForNotJoining,
      courseWatchMinutes,
      courseCompleted,
      hasCourseFeedback,
      courseFeedback,
      inquiredAboutSystemClass,
      neverWatchedDay123
    }
  }

  /**
   * 获取第四天的所有相关数据
   * @param chatId 客户聊天ID
   * @returns 第四天数据
   */
  private static async getDay4Data(chatId: string) {
    const courseStartTime = await DataService.getCourseStartTime(chatId)

    // 计算第四天的时间范围
    const day4StartTime = new Date(courseStartTime)
    day4StartTime.setDate(day4StartTime.getDate() + 3)
    const day4EndTime = new Date(day4StartTime)
    day4EndTime.setDate(day4EndTime.getDate() + 1)

    // 获取聊天历史
    const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)

    // 第四天的消息
    const day4Messages = chatHistory.filter((msg) => {
      const messageTime = new Date(msg.created_at)
      return messageTime >= day4StartTime && messageTime < day4EndTime
    })

    // 第四天的用户消息
    const day4UserMessages = day4Messages.filter((msg) => msg.role === 'user')

    // 前一天（第三天）的用户消息（用于对比）
    const day3StartTime = new Date(courseStartTime)
    day3StartTime.setDate(day3StartTime.getDate() + 2)
    const preDayMessages = chatHistory.filter((msg) => {
      const messageTime = new Date(msg.created_at)
      return msg.role === 'user' && messageTime >= day3StartTime && messageTime < day4StartTime
    })

    // 计算各项指标
    const conversationRounds = day4UserMessages.length
    const repliedToday = day4UserMessages.length > 0

    // 计算前一天对话轮次比例
    const preDayAverage = preDayMessages.length || 1 // 避免除零
    const preDayConversationRatio = conversationRounds / preDayAverage

    // 检查是否回复系统班相关信息
    const repliedSystemClassInfo = await this.checkRepliedSystemClassInfo(chatId, day4Messages)

    // 检查是否有负面反馈（历史累计）
    const hasNegativeFeedback = await this.checkHasNegativeFeedback(chatId)

    // 获取课程观看数据
    const courseWatchMinutes = await this.getCourseWatchMinutes(chatId, 4)
    const courseCompleted = await DataService.isCompletedCourse(chatId, { day: 4 })

    // 检查课程反馈
    const hasCourseFeedback = await this.hasRepliedAfterCourseFeeling(chatId, 4)
    const courseFeedback = hasCourseFeedback ? await this.analyzeCourseFeedback(chatId, 4) : 'none'

    // 检查是否提到补课
    const mentionedMakeupClass = await this.checkMentionedMakeupClass(chatId, day4Messages)

    return {
      conversationRounds,
      repliedToday,
      preDayConversationRatio,
      repliedSystemClassInfo,
      hasNegativeFeedback,
      courseWatchMinutes,
      courseCompleted,
      hasCourseFeedback,
      courseFeedback,
      mentionedMakeupClass
    }
  }

  /**
   * 检查是否回复参加但未参加
   * @param chatId 客户聊天ID
   * @param day1Messages 第一天的消息
   * @returns 是否回复参加但未参加
   */
  private static async checkRepliedJoinButNotJoin(chatId: string, day1Messages: any[]): Promise<boolean> {
    try {
      // 检查是否有表示要参加的消息
      const joinMessages = day1Messages.filter((msg) =>
        msg.role === 'user' &&
        (msg.content.includes('参加') || msg.content.includes('来') || msg.content.includes('好'))
      )

      if (joinMessages.length === 0) {
        return false
      }

      // 检查是否实际参加了课程（观看时长>0）
      const watchMinutes = await this.getCourseWatchMinutes(chatId, 1)
      return watchMinutes === 0
    } catch (error) {
      logger.error('检查回复参加但未参加失败', error)
      return false
    }
  }

  /**
   * 检查是否给出了不参加的原因
   * @param chatId 客户聊天ID
   * @param day1Messages 第一天的消息
   * @returns 是否给出了不参加的原因
   */
  private static async checkGaveReasonForNotJoining(chatId: string, day1Messages: any[]): Promise<boolean> {
    try {
      // 使用LLM分析是否给出了不参加的具体原因
      const userMessages = day1Messages
        .filter((msg) => msg.role === 'user')
        .map((msg) => msg.content)
        .join('\n')

      if (!userMessages.trim()) {
        return false
      }

      const prompt = SystemMessagePromptTemplate.fromTemplate(`请分析以下客户的第一天聊天记录，判断客户是否给出了不参加课程的具体原因。

客户聊天记录：
{chat_history}

判断标准：
- 客户明确说明了不能参加的原因（如：忙、有事、时间冲突等）
- 客户解释了自己的情况或困难
- 客户虽然没参加但表达了歉意或说明

请仅回答 "true" 或 "false"。`)

      const response = await LLM.predict(prompt, {
        model: 'gpt-4.1-mini',
        temperature: 0,
        meta: { chat_id: chatId }
      }, { chat_history: userMessages })

      return response.trim().toLowerCase() === 'true'
    } catch (error) {
      logger.error('检查不参加原因失败', error)
      return false
    }
  }

  /**
   * 分析打卡情感
   * @param chatId 客户聊天ID
   * @param day 天数
   * @returns 情感类型
   */
  private static async analyzeCheckInSentiment(chatId: string, day: number): Promise<string> {
    try {
      // 获取课后感悟内容
      const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)
      const courseStartTime = await DataService.getCourseStartTime(chatId)

      // 计算指定天的时间范围
      const dayStartTime = new Date(courseStartTime)
      dayStartTime.setDate(dayStartTime.getDate() + day - 1)

      const dayEndTime = new Date(dayStartTime)
      dayEndTime.setDate(dayEndTime.getDate() + 1)

      // 查找课后感悟相关的消息
      const dayMessages = chatHistory.filter((msg) => {
        const messageTime = new Date(msg.created_at)
        return msg.role === 'user' &&
               messageTime >= dayStartTime &&
               messageTime < dayEndTime
      })

      const userMessages = dayMessages.map((msg) => msg.content).join('\n')

      if (!userMessages.trim()) {
        return 'none'
      }

      const prompt = SystemMessagePromptTemplate.fromTemplate(`请分析以下客户的课后感悟，判断情感倾向和参与度。

客户感悟内容：
{chat_history}

请根据以下标准分类：
- "negative": 很差-10分，很好+10分 (消极、不满、抱怨)
- "neutral": 一般的分，很差-10分，很好+10分 (中性、简单回复)
- "positive": 比较积极/明确问题 (积极、有收获、有思考)
- "very_positive": 高意向/参与活跃 (非常积极、深度思考、主动反馈)

请仅回答分类标签。`)

      const response = await LLM.predict(prompt, {
        model: 'gpt-4.1-mini',
        temperature: 0,
        meta: { chat_id: chatId }
      }, { chat_history: userMessages })

      return response.trim().toLowerCase()
    } catch (error) {
      logger.error('分析打卡情感失败', error)
      return 'none'
    }
  }

  /**
   * 计算打卡感受分数
   * @param sentiment 情感类型
   * @returns 分数
   */
  private static calculateCheckInScore(sentiment: string): number {
    switch (sentiment) {
      case 'negative':
        return -10 // 很差-10分
      case 'neutral':
        return 0   // 一般的分
      case 'positive':
        return 10  // 比较积极+10分
      case 'very_positive':
        return 20  // 高意向+20分
      default:
        return 0
    }
  }

  /**
   * 计算第二天课程感受分数
   * @param feedback 反馈类型
   * @returns 分数
   */
  private static calculateCourseFeelingScore(feedback: string): number {
    switch (feedback) {
      case 'general':
        return 0   // 一般0分
      case 'good':
        return 10  // 好+10分
      case 'bad':
        return -10 // 不好-10分
      default:
        return 0
    }
  }

  /**
   * 计算第三天课程感受分数
   * @param feedback 反馈类型
   * @returns 分数
   */
  private static calculateDay3CourseFeelingScore(feedback: string): number {
    switch (feedback) {
      case 'general':
        return -10 // 一般-10分
      case 'bad':
        return -20 // 不好-20分
      case 'very_good':
        return 10  // 很好+10分
      default:
        return 0
    }
  }

  /**
   * 分析课程反馈
   * @param chatId 客户聊天ID
   * @param day 天数
   * @returns 反馈类型
   */
  private static async analyzeCourseFeedback(chatId: string, day: number): Promise<string> {
    try {
      // 获取课后感悟内容
      const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)
      const courseStartTime = await DataService.getCourseStartTime(chatId)

      // 计算指定天的时间范围
      const dayStartTime = new Date(courseStartTime)
      dayStartTime.setDate(dayStartTime.getDate() + day - 1)

      const dayEndTime = new Date(dayStartTime)
      dayEndTime.setDate(dayEndTime.getDate() + 1)

      // 查找课后感悟相关的消息
      const dayMessages = chatHistory.filter((msg) => {
        const messageTime = new Date(msg.created_at)
        return msg.role === 'user' &&
               messageTime >= dayStartTime &&
               messageTime < dayEndTime
      })

      const userMessages = dayMessages.map((msg) => msg.content).join('\n')

      if (!userMessages.trim()) {
        return 'none'
      }

      let prompt: string
      if (day === 2) {
        prompt = `请分析以下客户的第二天课程反馈，判断反馈类型。

客户反馈内容：
{chat_history}

请根据以下标准分类：
- "general": 一般的反馈
- "good": 好的反馈
- "bad": 不好的反馈

请仅回答分类标签。`
      } else if (day === 3) {
        prompt = `请分析以下客户的第三天课程反馈，判断反馈类型。

客户反馈内容：
{chat_history}

请根据以下标准分类：
- "general": 一般的反馈
- "bad": 不好的反馈
- "very_good": 很好的反馈

请仅回答分类标签。`
      } else {
        return 'none'
      }

      const promptTemplate = SystemMessagePromptTemplate.fromTemplate(prompt)
      const response = await LLM.predict(promptTemplate, {
        model: 'gpt-4.1-mini',
        temperature: 0,
        meta: { chat_id: chatId }
      }, { chat_history: userMessages })

      return response.trim().toLowerCase()
    } catch (error) {
      logger.error('分析课程反馈失败', error)
      return 'none'
    }
  }

  /**
   * 检查是否提到财富果园画面
   * @param chatId 客户聊天ID
   * @param dayMessages 当天消息
   * @returns 是否提到财富果园画面
   */
  private static async checkMentionedWealthGarden(chatId: string, dayMessages: any[]): Promise<boolean> {
    try {
      const userMessages = dayMessages
        .filter((msg) => msg.role === 'user')
        .map((msg) => msg.content)
        .join('\n')

      if (!userMessages.trim()) {
        return false
      }

      // 简单关键词检测
      const keywords = ['财富果园', '果园', '画面', '看到', '想象', '场景']
      return keywords.some((keyword) => userMessages.includes(keyword))
    } catch (error) {
      logger.error('检查财富果园画面失败', error)
      return false
    }
  }

  /**
   * 检查是否问询过系统班
   * @param chatId 客户聊天ID
   * @param dayMessages 当天消息
   * @returns 是否问询过系统班
   */
  private static async checkInquiredAboutSystemClass(chatId: string, dayMessages: any[]): Promise<boolean> {
    try {
      const userMessages = dayMessages
        .filter((msg) => msg.role === 'user')
        .map((msg) => msg.content)
        .join('\n')

      if (!userMessages.trim()) {
        return false
      }

      // 检测系统班相关关键词
      const keywords = ['系统班', '系统课', '报名', '价格', '费用', '怎么报', '如何报']
      return keywords.some((keyword) => userMessages.includes(keyword))
    } catch (error) {
      logger.error('检查系统班问询失败', error)
      return false
    }
  }

  /**
   * 检查客户是否已下单
   * @param chatId 客户聊天ID
   * @returns 是否已下单
   */
  private static async checkHasPurchased(chatId: string): Promise<boolean> {
    try {
      // 这里需要根据实际的订单系统来实现
      // 可能需要查询订单表或者调用订单服务
      // 暂时返回false，需要根据实际业务逻辑实现

      // 示例实现：检查聊天记录中是否有下单相关的关键词
      const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)
      const allUserMessages = chatHistory
        .filter((msg) => msg.role === 'user')
        .map((msg) => msg.content)
        .join('\n')

      const purchaseKeywords = ['已付款', '已下单', '已购买', '支付成功', '订单', '付费']
      return purchaseKeywords.some((keyword) => allUserMessages.includes(keyword))
    } catch (error) {
      logger.error('检查下单状态失败', error)
      return false
    }
  }

  /**
   * 检查是否回复系统班相关信息
   * @param chatId 客户聊天ID
   * @param dayMessages 当天消息
   * @returns 是否回复系统班相关信息
   */
  private static async checkRepliedSystemClassInfo(chatId: string, dayMessages: any[]): Promise<boolean> {
    try {
      const userMessages = dayMessages
        .filter((msg) => msg.role === 'user')
        .map((msg) => msg.content)
        .join('\n')

      if (!userMessages.trim()) {
        return false
      }

      // 检测系统班相关关键词
      const keywords = ['系统班', '系统课', '报名', '价格', '费用', '课程', '学习', '感兴趣']
      return keywords.some((keyword) => userMessages.includes(keyword))
    } catch (error) {
      logger.error('检查系统班信息回复失败', error)
      return false
    }
  }

  /**
   * 检查是否有负面反馈（历史累计）
   * @param chatId 客户聊天ID
   * @returns 是否有负面反馈
   */
  private static async checkHasNegativeFeedback(chatId: string): Promise<boolean> {
    try {
      const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)
      const allUserMessages = chatHistory
        .filter((msg) => msg.role === 'user')
        .map((msg) => msg.content)
        .join('\n')

      if (!allUserMessages.trim()) {
        return false
      }

      // 检测负面反馈关键词
      const negativeKeywords = ['不好', '没感觉', '没用', '浪费时间', '不满意', '差', '失望']
      return negativeKeywords.some((keyword) => allUserMessages.includes(keyword))
    } catch (error) {
      logger.error('检查负面反馈失败', error)
      return false
    }
  }

  /**
   * 检查是否提到补课
   * @param chatId 客户聊天ID
   * @param dayMessages 当天消息
   * @returns 是否提到补课
   */
  private static async checkMentionedMakeupClass(chatId: string, dayMessages: any[]): Promise<boolean> {
    try {
      const userMessages = dayMessages
        .filter((msg) => msg.role === 'user')
        .map((msg) => msg.content)
        .join('\n')

      if (!userMessages.trim()) {
        return false
      }

      // 检测补课相关关键词
      const keywords = ['补课', '重听', '再听', '错过', '没听到', '重新学']
      return keywords.some((keyword) => userMessages.includes(keyword))
    } catch (error) {
      logger.error('检查补课提及失败', error)
      return false
    }
  }

  /**
   * 计算第四天课程感受分数
   * @param feedback 反馈类型
   * @returns 分数
   */
  private static calculateDay4CourseFeelingScore(feedback: string): number {
    switch (feedback) {
      case 'good':
        return 10  // 好+10分
      case 'bad':
        return -20 // 不好-20分
      default:
        return 0
    }
  }
}

export { NewIntentCalculator, NewIntentScoreResult }
