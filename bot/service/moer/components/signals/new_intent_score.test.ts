import { NewIntentCalculator } from './new_intent_score'
import { DataService } from '../../getter/getData'
import { ChatStatStoreManager } from '../../storage/chat_state_store'
import * as fs from 'fs'
import * as path from 'path'

/**
 * 新意向度计算系统测试
 * 生成指定期数客户的累积意向度CSV报告
 */

describe('新意向度计算系统', () => {

  it('生成指定期数客户累积意向度CSV报告', async () => {
    const courseNo = 78 // 可以修改为其他期数
    const outputDir = path.join(process.cwd(), 'output')
    const csvFilePath = path.join(outputDir, `course_${courseNo}_cumulative_intent_scores.csv`)

    try {
      console.log(`🚀 开始生成第${courseNo}期客户累积意向度报告...`)

      // 确保输出目录存在
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true })
      }

      // 获取指定期数的所有客户
      const chats = await DataService.getChatsByCourseNo(courseNo)
      console.log(`📊 找到 ${chats.length} 个客户`)

      if (chats.length === 0) {
        console.log(`❌ 没有找到第${courseNo}期的客户`)
        return
      }

      // CSV表头
      const csvHeaders = [
        '客户ID',
        '客户姓名',
        '微信名',
        '接量期意向度',
        '第1天累积意向度',
        '第2天累积意向度',
        '第3天累积意向度',
        '第4天累积意向度',
        '接量期等级',
        '第1天等级',
        '第2天等级',
        '第3天等级',
        '第4天等级'
      ]

      // 初始化CSV内容
      let csvContent = `${csvHeaders.join(',')  }\n`

      // 处理每个客户（限制前20个客户以避免超时）
      const limitedChats = chats.slice(0, 20)
      console.log(`📝 处理前 ${limitedChats.length} 个客户...`)

      for (let i = 0; i < limitedChats.length; i++) {
        const chat = limitedChats[i]
        console.log(`\n处理客户 ${i + 1}/${limitedChats.length}: ${chat.contact.wx_name}`)

        try {
          // 初始化客户状态
          await ChatStatStoreManager.initState(chat.id)

          // 计算接量期意向度
          const preCourseResult = await NewIntentCalculator.calculatePreCourseIntentScore(chat.id)
          console.log(`  接量期: ${preCourseResult.intent_score}分`)

          // 计算各天累积意向度
          const day1Result = await NewIntentCalculator.calculateCumulativeIntentScore(chat.id, 1)
          console.log(`  第1天累积: ${day1Result.intent_score}分`)

          const day2Result = await NewIntentCalculator.calculateCumulativeIntentScore(chat.id, 2)
          console.log(`  第2天累积: ${day2Result.intent_score}分`)

          const day3Result = await NewIntentCalculator.calculateCumulativeIntentScore(chat.id, 3)
          console.log(`  第3天累积: ${day3Result.intent_score}分`)

          const day4Result = await NewIntentCalculator.calculateCumulativeIntentScore(chat.id, 4)
          console.log(`  第4天累积: ${day4Result.intent_score}分`)

          // 构建CSV行数据
          const csvRow = [
            chat.id,
            `"${chat.contact.wx_name || ''}"`,
            preCourseResult.intent_score,
            day1Result.intent_score,
            day2Result.intent_score,
            day3Result.intent_score,
            day4Result.intent_score,
          ]

          csvContent += `${csvRow.join(',')  }\n`

        } catch (error) {
          console.error(`  ❌ 处理客户失败: ${error instanceof Error ? error.message : String(error)}`)

          // 添加错误行
          const errorRow = [
            chat.id,
            `"${chat.contact.wx_name || ''}"`,
            'ERROR',
            'ERROR',
            'ERROR',
            'ERROR',
            'ERROR',
            'ERROR',
            'ERROR',
            'ERROR',
            'ERROR',
            'ERROR'
          ]
          csvContent += `${errorRow.join(',')  }\n`
        }
      }

      // 写入CSV文件
      fs.writeFileSync(csvFilePath, csvContent, 'utf8')

      console.log('\n✅ CSV报告生成完成!')
      console.log(`📁 文件路径: ${csvFilePath}`)
      console.log(`📊 处理客户数: ${limitedChats.length}`)

      // 显示文件内容预览
      console.log('\n📋 CSV内容预览:')
      const lines = csvContent.split('\n').slice(0, 6) // 显示前5行数据
      lines.forEach((line, index) => {
        if (line.trim()) {
          console.log(`${index === 0 ? '表头' : `客户${index}`}: ${line}`)
        }
      })

      expect(fs.existsSync(csvFilePath)).toBe(true)

    } catch (error) {
      console.error('❌ 生成CSV报告失败:', error)
      throw error
    }
  }, 600000) // 10分钟超时

  it('测试单个客户累积意向度计算', async () => {
    const courseNo = 78

    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(courseNo)
    if (chats.length === 0) {
      console.log('没有找到测试客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    console.log(`\n🧪 测试客户累积意向度: ${testChat.contact.wx_name}`)
    console.log('=' .repeat(60))

    // 计算接量期意向度
    const preCourseResult = await NewIntentCalculator.calculatePreCourseIntentScore(testChat.id)

    // 计算各天累积意向度
    for (let day = 1; day <= 4; day++) {
      try {
        const cumulativeResult = await NewIntentCalculator.calculateCumulativeIntentScore(testChat.id, day)
        const singleDayResult = await NewIntentCalculator.calculateCourseIntentScore(testChat.id, day)


        // 计算增量
        const previousCumulative = day === 1 ? preCourseResult.intent_score :
          (await NewIntentCalculator.calculateCumulativeIntentScore(testChat.id, day - 1)).intent_score
        const increment = cumulativeResult.intent_score - previousCumulative
        console.log(`   当天增量: ${increment > 0 ? '+' : ''}${increment}分`)

      } catch (error) {
        console.error(`   ❌ 第${day}天计算失败: ${error instanceof Error ? error.message : String(error)}`)
      }
    }

  }, 180000)

  it('验证累积计算逻辑', async () => {
    const courseNo = 78

    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(courseNo)
    if (chats.length === 0) {
      console.log('没有找到测试客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    console.log(`\n🔍 验证累积计算逻辑: ${testChat.contact.wx_name}`)

    // 手动计算累积分数
    const preCourseResult = await NewIntentCalculator.calculatePreCourseIntentScore(testChat.id)
    let manualCumulative = preCourseResult.intent_score

    console.log(`基础分数(接量期): ${manualCumulative}`)

    for (let day = 1; day <= 4; day++) {
      const dayIncrement = await (NewIntentCalculator as any).calculateSingleDayIntentScore(testChat.id, day)
      manualCumulative += dayIncrement.intent_score

      const autoCumulative = await NewIntentCalculator.calculateCumulativeIntentScore(testChat.id, day)

      console.log(`第${day}天:`)
      console.log(`  增量: ${dayIncrement.intent_score}`)
      console.log(`  手动累积: ${manualCumulative}`)
      console.log(`  自动累积: ${autoCumulative.intent_score}`)
      console.log(`  是否一致: ${manualCumulative === autoCumulative.intent_score ? '✅' : '❌'}`)

      expect(manualCumulative).toBe(autoCumulative.intent_score)
    }

  }, 180000)

  it('生成简化版CSV报告（前5个客户）', async () => {
    const courseNo = 78
    const outputDir = path.join(process.cwd(), 'output')
    const csvFilePath = path.join(outputDir, `course_${courseNo}_sample_intent_scores.csv`)

    try {
      console.log(`🚀 生成第${courseNo}期客户样本意向度报告...`)

      // 确保输出目录存在
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true })
      }

      // 获取前5个客户
      const chats = await DataService.getChatsByCourseNo(courseNo)
      const sampleChats = chats.slice(0, 5)

      if (sampleChats.length === 0) {
        console.log(`❌ 没有找到第${courseNo}期的客户`)
        return
      }

      // CSV表头（包含增量信息）
      const csvHeaders = [
        '客户ID',
        '客户姓名',
        '微信名',
        '接量期分数',
        '第1天增量',
        '第1天累积',
        '第2天增量',
        '第2天累积',
        '第3天增量',
        '第3天累积',
        '第4天增量',
        '第4天累积',
        '最终等级'
      ]

      let csvContent = `${csvHeaders.join(',')  }\n`

      for (let i = 0; i < sampleChats.length; i++) {
        const chat = sampleChats[i]
        console.log(`\n处理样本客户 ${i + 1}/${sampleChats.length}: ${chat.contact.wx_name}`)

        try {
          await ChatStatStoreManager.initState(chat.id)

          // 计算接量期分数
          const preCourseResult = await NewIntentCalculator.calculatePreCourseIntentScore(chat.id)
          const previousScore = preCourseResult.intent_score

          const increments = []
          const cumulatives = [preCourseResult.intent_score]

          // 计算各天增量和累积
          for (let day = 1; day <= 4; day++) {
            const dayIncrement = await (NewIntentCalculator as any).calculateSingleDayIntentScore(chat.id, day)
            const cumulativeResult = await NewIntentCalculator.calculateCumulativeIntentScore(chat.id, day)

            increments.push(dayIncrement.intent_score)
            cumulatives.push(cumulativeResult.intent_score)

            console.log(`  第${day}天: 增量${dayIncrement.intent_score}, 累积${cumulativeResult.intent_score}`)
          }

          // 构建CSV行
          const csvRow = [
            chat.id,
            `"${chat.contact.wx_name || ''}"`,
            preCourseResult.intent_score,
            ...increments,
            ...cumulatives.slice(1), // 排除接量期分数
            `"${cumulatives[cumulatives.length - 1] >= 60 ? 'high' : cumulatives[cumulatives.length - 1] >= 40 ? 'medium' : 'low'}"`
          ]

          csvContent += `${csvRow.join(',')  }\n`

        } catch (error) {
          console.error(`  ❌ 处理失败: ${error instanceof Error ? error.message : String(error)}`)
        }
      }

      // 写入文件
      fs.writeFileSync(csvFilePath, csvContent, 'utf8')

      console.log('\n✅ 样本CSV报告生成完成!')
      console.log(`📁 文件路径: ${csvFilePath}`)

      expect(fs.existsSync(csvFilePath)).toBe(true)

    } catch (error) {
      console.error('❌ 生成样本CSV报告失败:', error)
      throw error
    }
  }, 300000)
})
