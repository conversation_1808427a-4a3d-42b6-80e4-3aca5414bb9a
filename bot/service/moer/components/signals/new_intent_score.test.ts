import { NewIntentCalculator } from './new_intent_score'
import { DataService } from '../../getter/getData'
import { ChatStatStoreManager } from '../../storage/chat_state_store'
import { ChatHistoryService } from '../chat_history/chat_history'

describe('新意向度计算测试', function () {
  beforeAll(() => {
    // 测试前的初始化
  })

  it('测试进量期意向度计算 - 单个客户', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    console.log(`测试客户: ${testChat.contact.wx_name} (${testChat.id})`)

    // 初始化客户状态
    await ChatStatStoreManager.initState(testChat.id)

    // 获取课程开始时间用于验证
    const courseStartTime = await DataService.getCourseStartTime(testChat.id)
    console.log(`课程开始时间: ${courseStartTime}`)

    // 测试进量期意向度计算
    const preCourseResult = await NewIntentCalculator.calculatePreCourseIntentScore(testChat.id)
    console.log('进量期意向度结果:', {
      客户昵称: testChat.contact.wx_name,
      意向度分数: preCourseResult.intent_score,
      有足够对话: preCourseResult.pre_course_data?.has_conversation,
      完成先导课: preCourseResult.pre_course_data?.completed_leading_course,
      完成挖需: preCourseResult.pre_course_data?.completed_need_mining,
      完成能量测评: preCourseResult.pre_course_data?.completed_energy_test,
      接量期对话轮数: preCourseResult.pre_course_data?.conversation_rounds
    })

  }, 120000)

  it('测试困扰表达检查', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    // 使用反射调用私有方法进行测试
    const hasExpressedTroubles = await (NewIntentCalculator as any).checkHasExpressedTroubles(testChat.id)
    console.log(`客户 ${testChat.contact.wx_name} 是否表达过困扰: ${hasExpressedTroubles}`)

  }, 60000)

  it('测试先导课分数计算', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    // 使用反射调用私有方法进行测试
    const leadingCourseScore = await (NewIntentCalculator as any).calculateLeadingCourseScore(testChat.id)
    console.log(`客户 ${testChat.contact.wx_name} 先导课分数: ${leadingCourseScore}`)

  }, 60000)

  it('测试意向调研回复检查', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    // 使用反射调用私有方法进行测试
    const repliedIntentionQuery = await (NewIntentCalculator as any).checkRepliedIntentionQuery(testChat.id)
    console.log(`客户 ${testChat.contact.wx_name} 是否回复过意向调研: ${repliedIntentionQuery}`)

  }, 60000)

  it('测试多个客户的进量期意向度', async () => {
    // 获取多个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    const testChats = chats.slice(0, 5) // 只测试前5个客户

    console.log(`测试 ${testChats.length} 个客户的进量期意向度`)

    for (const chat of testChats) {
      try {
        await ChatStatStoreManager.initState(chat.id)
        const result = await NewIntentCalculator.calculatePreCourseIntentScore(chat.id)

        console.log(`客户: ${chat.contact.wx_name}, 分数: ${result.intent_score}`)
      } catch (error) {
        console.error(`处理客户 ${chat.contact.wx_name} 失败:`, error)
      }
    }

  }, 300000)

  it('对比新旧意向度计算结果', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    // 导入旧的意向度计算器
    const { IntentCalculator } = await import('./intent_score')

    console.log(`\n对比客户: ${testChat.contact.wx_name}`)

    // 旧版本计算
    const oldResult = await IntentCalculator.calculateIntentScore(testChat.id)
    console.log('旧版本结果:', {
      stage: oldResult.stage,
      intent_score: oldResult.intent_score,
      intent_level: oldResult.intent_level
    })

    // 新版本计算
    const newResult = await NewIntentCalculator.calculateIntentScore(testChat.id)
    console.log('新版本结果:', {
      stage: newResult.stage,
      intent_score: newResult.intent_score,
    })

    console.log('分数差异:', newResult.intent_score - oldResult.intent_score)

  }, 120000)

  it('测试进群状态检查', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    // 检查配置
    const { Config } = await import('../../../../config/config')
    console.log('微信配置状态:')
    console.log(`  - wechatConfig.id: ${Config.setting.wechatConfig?.id ? '已配置' : '未配置'}`)
    console.log(`  - classGroupId: ${Config.setting.wechatConfig?.classGroupId ? '已配置' : '未配置'}`)

    // 获取客户的微信ID
    const moerId = await DataService.getMoerIdByChatId(testChat.id)
    const userId = moerId ? await DataService.getWxIdByMoerId(moerId) : null

    console.log('客户信息:')
    console.log(`  - moerId: ${moerId || '无'}`)
    console.log(`  - userId: ${userId || '无'}`)

    if (userId && Config.setting.wechatConfig?.id && Config.setting.wechatConfig?.classGroupId) {
      try {
        const isInGroup = await DataService.isInGroup(userId)
        console.log(`客户 ${testChat.contact.wx_name} 是否在群内: ${isInGroup}`)
      } catch (error) {
        console.log(`检查进群状态失败: ${error instanceof Error ? error.message : String(error)}`)
      }
    } else {
      console.log('无法检查进群状态 - 缺少必要信息')
    }

  }, 60000)

  it('测试接量期数据筛选', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    // 获取课程开始时间
    const courseStartTime = await DataService.getCourseStartTime(testChat.id)
    console.log(`客户 ${testChat.contact.wx_name} 课程开始时间: ${courseStartTime}`)

    // 获取所有聊天历史
    const allChatHistory = await ChatHistoryService.getChatHistoryByChatId(testChat.id)

    // 筛选接量期的聊天记录
    const preCourseHistory = allChatHistory.filter((msg) => {
      const messageTime = new Date(msg.created_at)
      return messageTime < courseStartTime
    })

    // 筛选上课期的聊天记录
    const courseHistory = allChatHistory.filter((msg) => {
      const messageTime = new Date(msg.created_at)
      return messageTime >= courseStartTime
    })

    console.log(`总聊天记录数: ${allChatHistory.length}`)
    console.log(`接量期聊天记录数: ${preCourseHistory.length}`)
    console.log(`上课期聊天记录数: ${courseHistory.length}`)

    // 显示接量期的用户消息数量
    const preCourseUserMessages = preCourseHistory.filter((msg) => msg.role === 'user')
    console.log(`接量期用户消息数: ${preCourseUserMessages.length}`)

    // 显示最早和最晚的接量期消息时间
    if (preCourseHistory.length > 0) {
      const earliestPreCourse = new Date(Math.min(...preCourseHistory.map((msg) => new Date(msg.created_at).getTime())))
      const latestPreCourse = new Date(Math.max(...preCourseHistory.map((msg) => new Date(msg.created_at).getTime())))
      console.log(`接量期消息时间范围: ${earliestPreCourse} 到 ${latestPreCourse}`)
    }

  }, 60000)

  it('测试第一天意向度计算', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    console.log(`\n测试客户第一天意向度: ${testChat.contact.wx_name}`)

    // 获取课程开始时间
    const courseStartTime = await DataService.getCourseStartTime(testChat.id)
    console.log(`课程开始时间: ${courseStartTime}`)

    // 计算第一天意向度
    const day1Result = await NewIntentCalculator.calculateCourseIntentScore(testChat.id, 1)

    console.log('第一天意向度结果:', {
      意向度分数: day1Result.intent_score,
      对话轮数: day1Result.course_data?.conversation_rounds,
      观看分钟数: day1Result.course_data?.course_watch_minutes,
      有打卡: day1Result.course_data?.replied_after_course_feeling
    })

    // 测试第一天数据获取
    const day1Data = await (NewIntentCalculator as any).getDay1Data(testChat.id)
    console.log('第一天详细数据:', {
      对话轮数: day1Data.conversationRounds,
      总回复数: day1Data.totalReplies,
      从未回复: day1Data.neverReplied,
      开课前对话比例: day1Data.preCourseConversationRatio.toFixed(2),
      回复参加但未参加: day1Data.repliedJoinButNotJoin,
      给出不参加原因: day1Data.gaveReasonForNotJoining,
      观看分钟数: day1Data.courseWatchMinutes,
      完成课程: day1Data.courseCompleted,
      有打卡: day1Data.hasCheckedIn,
      打卡情感: day1Data.checkInSentiment
    })

  }, 180000)

  it('测试第一天各项子功能', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    console.log(`\n测试第一天子功能: ${testChat.contact.wx_name}`)

    // 获取第一天消息
    const chatHistory = await ChatHistoryService.getChatHistoryByChatId(testChat.id)
    const courseStartTime = await DataService.getCourseStartTime(testChat.id)

    const day1StartTime = new Date(courseStartTime)
    const day1EndTime = new Date(courseStartTime)
    day1EndTime.setDate(day1EndTime.getDate() + 1)

    const day1Messages = chatHistory.filter((msg) => {
      const messageTime = new Date(msg.created_at)
      return messageTime >= day1StartTime && messageTime < day1EndTime
    })

    console.log(`第一天消息数量: ${day1Messages.length}`)

    // 测试各项检查
    try {
      const repliedJoinButNotJoin = await (NewIntentCalculator as any).checkRepliedJoinButNotJoin(testChat.id, day1Messages)
      console.log(`回复参加但未参加: ${repliedJoinButNotJoin}`)
    } catch (error) {
      console.log(`回复参加但未参加检查失败: ${error instanceof Error ? error.message : String(error)}`)
    }

    try {
      const gaveReason = await (NewIntentCalculator as any).checkGaveReasonForNotJoining(testChat.id, day1Messages)
      console.log(`给出不参加原因: ${gaveReason}`)
    } catch (error) {
      console.log(`不参加原因检查失败: ${error instanceof Error ? error.message : String(error)}`)
    }

    try {
      const sentiment = await (NewIntentCalculator as any).analyzeCheckInSentiment(testChat.id, 1)
      console.log(`打卡情感分析: ${sentiment}`)

      const sentimentScore = (NewIntentCalculator as any).calculateCheckInScore(sentiment)
      console.log(`打卡情感分数: ${sentimentScore}`)
    } catch (error) {
      console.log(`打卡情感分析失败: ${error instanceof Error ? error.message : String(error)}`)
    }

  }, 180000)

  it('测试第二天意向度计算', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    console.log(`\n测试客户第二天意向度: ${testChat.contact.wx_name}`)

    // 计算第二天意向度
    const day2Result = await NewIntentCalculator.calculateCourseIntentScore(testChat.id, 2)

    console.log('第二天意向度结果:', {
      意向度分数: day2Result.intent_score,
      对话轮数: day2Result.course_data?.conversation_rounds,
      观看分钟数: day2Result.course_data?.course_watch_minutes,
      有反馈: day2Result.course_data?.replied_after_course_feeling
    })

    // 测试第二天数据获取
    const day2Data = await (NewIntentCalculator as any).getDay2Data(testChat.id)
    console.log('第二天详细数据:', {
      对话轮数: day2Data.conversationRounds,
      总回复数: day2Data.totalReplies,
      开课前对话比例: day2Data.preCourseConversationRatio.toFixed(2),
      课程反馈: day2Data.courseFeedback,
      提到财富果园: day2Data.mentionedWealthGarden,
      第一二天都没看: day2Data.neverWatchedDay1And2
    })

  }, 180000)

  it('测试第三天意向度计算', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    console.log(`\n测试客户第三天意向度: ${testChat.contact.wx_name}`)

    // 计算第三天意向度
    const day3Result = await NewIntentCalculator.calculateCourseIntentScore(testChat.id, 3)

    console.log('第三天意向度结果:', {
      意向度分数: day3Result.intent_score,
      对话轮数: day3Result.course_data?.conversation_rounds,
      观看分钟数: day3Result.course_data?.course_watch_minutes,
      有反馈: day3Result.course_data?.replied_after_course_feeling
    })

    // 测试第三天数据获取
    const day3Data = await (NewIntentCalculator as any).getDay3Data(testChat.id)
    console.log('第三天详细数据:', {
      对话轮数: day3Data.conversationRounds,
      总回复数: day3Data.totalReplies,
      前两天对话比例: day3Data.preTwoDaysConversationRatio.toFixed(2),
      课程反馈: day3Data.courseFeedback,
      问询系统班: day3Data.inquiredAboutSystemClass,
      三天都没看: day3Data.neverWatchedDay123
    })

  }, 180000)

  it('测试第四天意向度计算（去除下单人群）', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    console.log(`\n测试客户第四天意向度: ${testChat.contact.wx_name}`)

    // 计算第四天意向度
    const day4Result = await NewIntentCalculator.calculateCourseIntentScore(testChat.id, 4)

    console.log('第四天意向度结果:', {
      意向度分数: day4Result.intent_score,
      对话轮数: day4Result.course_data?.conversation_rounds,
      观看分钟数: day4Result.course_data?.course_watch_minutes,
      有反馈: day4Result.course_data?.replied_after_course_feeling,
      已下单: day4Result.course_data?.has_purchased
    })

    // 测试第四天数据获取
    const day4Data = await (NewIntentCalculator as any).getDay4Data(testChat.id)
    console.log('第四天详细数据:', {
      对话轮数: day4Data.conversationRounds,
      当天回复: day4Data.repliedToday,
      前一天对话比例: day4Data.preDayConversationRatio.toFixed(2),
      回复系统班信息: day4Data.repliedSystemClassInfo,
      有负面反馈: day4Data.hasNegativeFeedback,
      课程反馈: day4Data.courseFeedback,
      提到补课: day4Data.mentionedMakeupClass
    })

    // 测试下单检查
    const hasPurchased = await (NewIntentCalculator as any).checkHasPurchased(testChat.id)
    console.log('下单状态检查:', hasPurchased)

  }, 180000)

  it('测试所有天数意向度计算', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    console.log(`\n测试客户所有天数意向度: ${testChat.contact.wx_name}`)

    // 计算所有天数的意向度
    for (let day = 1; day <= 4; day++) {
      try {
        const result = await NewIntentCalculator.calculateCourseIntentScore(testChat.id, day)
        console.log(`第${day}天意向度:`, {
          分数: result.intent_score,
          对话轮数: result.course_data?.conversation_rounds,
          观看分钟数: result.course_data?.course_watch_minutes
        })
      } catch (error) {
        console.log(`第${day}天计算失败:`, error instanceof Error ? error.message : String(error))
      }
    }

  }, 300000)
})
