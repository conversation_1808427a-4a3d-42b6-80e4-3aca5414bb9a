import { NewIntentCalculator } from './new_intent_score'
import { DataService } from '../../getter/getData'
import { ChatStatStoreManager } from '../../storage/chat_state_store'

describe('新意向度计算测试', function () {
  beforeAll(() => {
    // 测试前的初始化
  })

  it('测试进量期意向度计算 - 单个客户', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    console.log(`测试客户: ${testChat.contact.wx_name} (${testChat.id})`)

    // 初始化客户状态
    await ChatStatStoreManager.initState(testChat.id)

    // 测试进量期意向度计算
    const preCourseResult = await NewIntentCalculator.calculatePreCourseIntentScore(testChat.id)
    console.log('进量期意向度结果:', {
      客户昵称: testChat.contact.wx_name,
      意向度分数: preCourseResult.intent_score,
      意向度等级: preCourseResult.intent_level,
      有足够对话: preCourseResult.pre_course_data?.has_conversation,
      完成先导课: preCourseResult.pre_course_data?.completed_leading_course,
      完成挖需: preCourseResult.pre_course_data?.completed_need_mining,
      完成能量测评: preCourseResult.pre_course_data?.completed_energy_test,
      对话轮数: preCourseResult.pre_course_data?.conversation_rounds
    })

  }, 120000)

  it('测试困扰表达检查', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    // 使用反射调用私有方法进行测试
    const hasExpressedTroubles = await (NewIntentCalculator as any).checkHasExpressedTroubles(testChat.id)
    console.log(`客户 ${testChat.contact.wx_name} 是否表达过困扰: ${hasExpressedTroubles}`)

  }, 60000)

  it('测试先导课分数计算', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    // 使用反射调用私有方法进行测试
    const leadingCourseScore = await (NewIntentCalculator as any).calculateLeadingCourseScore(testChat.id)
    console.log(`客户 ${testChat.contact.wx_name} 先导课分数: ${leadingCourseScore}`)

  }, 60000)

  it('测试意向调研回复检查', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    // 使用反射调用私有方法进行测试
    const repliedIntentionQuery = await (NewIntentCalculator as any).checkRepliedIntentionQuery(testChat.id)
    console.log(`客户 ${testChat.contact.wx_name} 是否回复过意向调研: ${repliedIntentionQuery}`)

  }, 60000)

  it('测试多个客户的进量期意向度', async () => {
    // 获取多个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    const testChats = chats.slice(0, 5) // 只测试前5个客户

    console.log(`测试 ${testChats.length} 个客户的进量期意向度`)

    for (const chat of testChats) {
      try {
        await ChatStatStoreManager.initState(chat.id)
        const result = await NewIntentCalculator.calculatePreCourseIntentScore(chat.id)
        
        console.log(`客户: ${chat.contact.wx_name}, 分数: ${result.intent_score}, 等级: ${result.intent_level}`)
      } catch (error) {
        console.error(`处理客户 ${chat.contact.wx_name} 失败:`, error)
      }
    }

  }, 300000)

  it('对比新旧意向度计算结果', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    // 导入旧的意向度计算器
    const { IntentCalculator } = await import('./intent_score')
    
    console.log(`\n对比客户: ${testChat.contact.wx_name}`)
    
    // 旧版本计算
    const oldResult = await IntentCalculator.calculateIntentScore(testChat.id)
    console.log('旧版本结果:', {
      stage: oldResult.stage,
      intent_score: oldResult.intent_score,
      intent_level: oldResult.intent_level
    })

    // 新版本计算
    const newResult = await NewIntentCalculator.calculateIntentScore(testChat.id)
    console.log('新版本结果:', {
      stage: newResult.stage,
      intent_score: newResult.intent_score,
      intent_level: newResult.intent_level
    })

    console.log('分数差异:', newResult.intent_score - oldResult.intent_score)

  }, 120000)

  it('测试进群状态检查', async () => {
    // 获取一个测试客户
    const chats = await DataService.getChatsByCourseNo(78)
    if (chats.length === 0) {
      console.log('没有找到78期的客户')
      return
    }

    const testChat = chats[0]
    await ChatStatStoreManager.initState(testChat.id)

    // 获取客户的微信ID
    const moerId = await DataService.getMoerIdByChatId(testChat.id)
    const userId = moerId ? await DataService.getWxIdByMoerId(moerId) : null
    
    if (userId) {
      const isInGroup = await DataService.isInGroup(userId)
      console.log(`客户 ${testChat.contact.wx_name} 是否在群内: ${isInGroup}`)
    } else {
      console.log(`客户 ${testChat.contact.wx_name} 没有绑定微信ID`)
    }

  }, 60000)
})
