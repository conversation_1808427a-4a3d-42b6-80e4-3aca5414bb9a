import { DataService } from '../../getter/getData'
import { ChatHistoryService } from '../chat_history/chat_history'
import { ChatStateStore } from '../../storage/chat_state_store'
import { DateHelper } from '../../../../lib/date/date'
import logger from '../../../../model/logger/logger'

interface IntentScoreResult {
  stage: 'pre_course' | 'day1' | 'day2' | 'day3' | 'day4'
  intent_score: number
  intent_level: '低' | '中' | '中高' | '高'

  // 韵如给的进量期意向判断标准
  pre_course_data?: {
    has_conversation: boolean
    completed_leading_course: boolean
    completed_intention_query: boolean
    completed_energy_test: boolean
    conversation_rounds: number // 这个不是标准，用来判断对话轮次变化
  }

  // 上课期数据
  course_data?: {
    day: number
    conversation_rounds: number
    course_watch_percentage: number
    replied_after_course_feeling: boolean
  }

  // 意向度差值记录
  intent_score_diff?: {
    from_pre_course?: number
    from_day1?: number
    from_day2?: number
    from_day3?: number
  }
}

class IntentCalculator {

  /**
   * 计算客户意向度（具体什么时候调用还需要韵如确定）
   * @param chatId 客户聊天ID
   * @returns 意向度计算结果 IntentScoreResult，有课程阶段、意向分数和意向度tag
   */
  public static async calculateIntentScore(chatId: string): Promise<IntentScoreResult> {
    try {
      // 判断客户当前是在进量期还是上课期
      const currentTime = await DataService.getCurrentTime(chatId)

      if (!currentTime.is_course_week) {
        return await this.calculatePreCourseIntentScore(chatId)
      } else {
        return await this.calculateCourseIntentScore(chatId, currentTime.day)
      }
    } catch (error) {
      logger.error('计算意向度失败', error)
      throw error
    }
  }

  /**
   * 计算进量期意向度
   * @param chatId 客户聊天ID
   * @returns 进量期意向度结果
   */
  private static async calculatePreCourseIntentScore(chatId: string): Promise<IntentScoreResult> {
    const conversationRounds = await ChatHistoryService.getUserMessageCount(chatId)
    const hasConversation = conversationRounds > 0

    // 先导课
    const completedLeadingCourse = await DataService.isCompletedCourse(chatId, { day: 0 })

    // 完成挖需（对话轮数>15）
    const completedNeedMining = conversationRounds > 15

    // 能量测评
    const chatState = ChatStateStore.getFlags(chatId)
    const completedEnergyTest = Boolean(chatState.is_complete_energy_test || chatState.is_complete_energy_test_analyze)

    // 每满足一项+25分
    let intentScore = 0
    if (hasConversation) intentScore += 25
    if (completedLeadingCourse) intentScore += 25
    if (completedNeedMining) intentScore += 25
    if (completedEnergyTest) intentScore += 25

    const intentLevel = this.getIntentLevel(intentScore)

    return {
      stage: 'pre_course',
      intent_score: intentScore,
      intent_level: intentLevel,
      pre_course_data: {
        has_conversation: hasConversation,
        completed_leading_course: completedLeadingCourse,
        completed_intention_query: completedNeedMining,
        completed_energy_test: completedEnergyTest,
        conversation_rounds: conversationRounds
      }
    }
  }

  /**
   * 计算上课期意向度
   * @param chatId 客户聊天ID
   * @param currentDay 当前天数（1-4）
   * @returns 上课期意向度结果
   */
  private static async calculateCourseIntentScore(chatId: string, currentDay: number): Promise<IntentScoreResult> {
    if (currentDay < 1 || currentDay > 4) {
      throw new Error('当前天数必须在1-4之间')
    }
    const conversationRounds = await this.getDayConversationRounds(chatId, currentDay)

    const courseWatchPercentage = await this.getCourseWatchPercentage(chatId, currentDay)

    const repliedAfterCourseFeeling = await this.hasRepliedAfterCourseFeeling(chatId, currentDay)

    const intentScore = this.calculateCourseIntentScoreValue(
      conversationRounds,
      courseWatchPercentage,
      repliedAfterCourseFeeling
    )

    // 确定意向度等级
    const intentLevel = this.getIntentLevel(intentScore)

    // 计算意向度差值
    const intentScoreDiff = await this.calculateIntentScoreDiff(chatId, currentDay, intentScore)

    return {
      stage: `day${currentDay}` as 'day1' | 'day2' | 'day3' | 'day4',
      intent_score: intentScore,
      intent_level: intentLevel,
      course_data: {
        day: currentDay,
        conversation_rounds: conversationRounds,
        course_watch_percentage: courseWatchPercentage,
        replied_after_course_feeling: repliedAfterCourseFeeling
      },
      intent_score_diff: intentScoreDiff
    }
  }

  /**
   * 获取指定天的对话轮数
   * @param chatId 客户聊天ID
   * @param day 天数（1-4）
   * @returns 对话轮数
   */
  private static async getDayConversationRounds(chatId: string, day: number): Promise<number> {
    try {
      // 获取课程开始时间
      const courseStartTime = await DataService.getCourseStartTime(chatId)

      // 计算指定天的开始和结束时间
      const dayStartTime = new Date(courseStartTime)
      dayStartTime.setDate(dayStartTime.getDate() + day - 1)
      dayStartTime.setHours(0, 0, 0, 0)

      const dayEndTime = new Date(dayStartTime)
      dayEndTime.setHours(23, 59, 59, 999)

      // 获取该时间段内的聊天历史
      const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)

      // 筛选指定时间段内的用户消息
      const dayMessages = chatHistory.filter((message) => {
        const messageTime = new Date(message.created_at)
        return message.role === 'user' &&
               messageTime >= dayStartTime &&
               messageTime <= dayEndTime
      })

      return dayMessages.length
    } catch (error) {
      logger.error(`获取Day${day}对话轮数失败`, error)
      return 0
    }
  }

  /**
   * 获取课程观看百分比
   * @param chatId 客户聊天ID
   * @param day 天数（1-4）
   * @returns 观看百分比（0-100）
   */
  private static async getCourseWatchPercentage(chatId: string, day: number): Promise<number> {
    try {
      // 取moerId
      const moerId = await DataService.getMoerIdByChatId(chatId)
      if (!moerId) {
        return 0
      }

      // 取课程信息
      const courseMap = await DataService.getMoerCourses(moerId)

      // 优先获取直播课程状态，如果没有则获取录播课程状态
      let courseStatus
      if (courseMap[day] && courseMap[day].live) {
        courseStatus = await DataService.getMoerCourseStatus(courseMap[day].live!)
      } else if (courseMap[day] && courseMap[day].record) {
        courseStatus = await DataService.getMoerCourseStatus(courseMap[day].record!)
      } else {
        return 0
      }

      if (!courseStatus || !courseStatus.duration) {
        return 0
      }

      let playbackTime = courseStatus.playbackTime
      if (typeof playbackTime === 'string') {
        playbackTime = Number(playbackTime)
      }

      // 计算观看百分比
      const percentage = (playbackTime / courseStatus.duration) * 100
      return Math.min(100, Math.max(0, percentage)) // 确保在0-100范围内
    } catch (error) {
      logger.error(`获取Day${day}课程观看百分比失败`, error)
      return 0
    }
  }

  /**
   * 检查是否回复了课后感悟
   * @param chatId 客户聊天ID
   * @param day 天数（1-4）
   * @returns 是否回复了课后感悟
   */
  private static async hasRepliedAfterCourseFeeling(chatId: string, day: number): Promise<boolean> {
    try {
      const chatState = ChatStateStore.get(chatId).state

      // 根据天数检查对应的课后感悟完成状态
      const dayStatusMap = {
        1: 'is_complete_day1_homework_feedback',
        2: 'is_complete_day2_homework_feedback',
        3: 'is_complete_day3_homework_feedback',
        4: 'is_complete_day4_homework_feedback'
      }

      const status = dayStatusMap[day as keyof typeof dayStatusMap]
      return Boolean(chatState[status])
    } catch (error) {
      logger.error(`检查Day${day}课后感悟失败`, error)
      return false
    }
  }

  /**
   * 计算上课期意向度分数
   * @param conversationRounds 对话轮数
   * @param courseWatchPercentage 课程观看百分比
   * @param repliedAfterCourseFeeling 是否回复课后感悟
   * @returns 意向度分数
   */
  private static calculateCourseIntentScoreValue(
    conversationRounds: number,
    courseWatchPercentage: number,
    repliedAfterCourseFeeling: boolean
  ): number {
    // 对话轮数分数：每轮对话3分，最高30分
    const conversationScore = Math.min(conversationRounds * 3, 30)

    // 课程观看百分比分数：观看百分比 * 0.5，最高50分
    const watchScore = Math.min(courseWatchPercentage * 0.5, 50)

    // 课后感悟分数：回复了得20分
    const feelingScore = repliedAfterCourseFeeling ? 20 : 0

    return conversationScore + watchScore + feelingScore
  }

  /**
   * 根据分数确定意向度等级
   * @param score 意向度分数
   * @returns 意向度等级
   */
  private static getIntentLevel(score: number): '低' | '中' | '中高' | '高' {
    if (score >= 100) return '高'
    if (score >= 75) return '中高'
    if (score >= 50) return '中'
    return '低'
  }

  /**
   * 计算意向度差值
   * @param chatId 客户聊天ID
   * @param currentDay 当前天数
   * @param currentScore 当前意向度分数
   * @returns 意向度差值对象
   */
  private static async calculateIntentScoreDiff(
    chatId: string,
    currentDay: number,
    currentScore: number
  ): Promise<{
    from_pre_course?: number
    from_day1?: number
    from_day2?: number
    from_day3?: number
  }> {
    const diff: any = {}

    try {
      // 计算与进量期的差值
      const preCourseResult = await this.calculatePreCourseIntentScore(chatId)
      diff.from_pre_course = currentScore - preCourseResult.intent_score

      // 计算与之前天数的差值
      for (let day = 1; day < currentDay; day++) {
        const previousDayScore = await this.getPreviousDayScore(chatId, day)
        if (previousDayScore !== null) {
          diff[`from_day${day}`] = currentScore - previousDayScore
        }
      }
    } catch (error) {
      logger.error('计算意向度差值失败', error)
    }

    return diff
  }

  /**
   * 获取之前某天的意向度分数
   * @param chatId 客户聊天ID
   * @param day 天数
   * @returns 意向度分数，如果无法获取则返回null
   */
  private static async getPreviousDayScore(chatId: string, day: number): Promise<number | null> {
    try {
      // 获取当天的对话轮数
      const conversationRounds = await this.getDayConversationRounds(chatId, day)

      // 获取课程观看百分比
      const courseWatchPercentage = await this.getCourseWatchPercentage(chatId, day)

      // 检查是否回复了课后感悟
      const repliedAfterCourseFeeling = await this.hasRepliedAfterCourseFeeling(chatId, day)

      // 计算意向度分数
      return this.calculateCourseIntentScoreValue(
        conversationRounds,
        courseWatchPercentage,
        repliedAfterCourseFeeling
      )
    } catch (error) {
      logger.error(`获取Day${day}意向度分数失败`, error)
      return null
    }
  }
}

export { IntentCalculator, IntentScoreResult }