import { Config } from '../../../../../config/config'
import { ChatDB } from '../../../database/chat'
import { JuziAPI } from '../../../../../lib/juzi/api'
import { DataService } from '../../../getter/getData'
import { startTasks } from '../schedule/task_starter'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { sleep } from 'openai/core'
import { MessageSender } from '../../message/message_send'
import { HumanTransfer, HumanTransferType } from '../../human_transfer/human_transfer'
import { IWecomMsgType } from '../../../../../lib/juzi/type'
import { MoerNode } from '../nodes/type'
import { FlowTask } from '../../schedule/silent_requestion'
import { MoerAPI } from '../../../../../model/moer_api/moer'
import { DateHelper } from '../../../../../lib/date/date'
import { inviteToGroup } from './inviteToGroup'
import { addPreCourseCheckTask } from '../schedule/task/sendWelcomeMessage'
import logger from '../../../../../model/logger/logger'
import { FlowTaskType } from '../../schedule/silent_reask_tasks'

export class NewCourseUser {
  /**
   * 创建 客户对应的信息，绑定手机号 和 MoerId, 如果返回 false, 表示客户之前已经购买过课程
   * @param userId
   * @param chatId
   * @param courseNo
   * @param phoneNumber
   * @param moerId
   */
  public static async create(userId: string, chatId: string, courseNo: number, phoneNumber: string, moerId: string) : Promise<boolean> {
    // 内部员工锁定期数
    if (Config.isInternalMember(userId)) {
      courseNo = DataService.getNextWeekCourseNo() // 默认按照下周开课
    }

    ChatStateStore.update(chatId, {
      nextStage: MoerNode.FreeTalk
    })

    await JuziAPI.addPhoneNumber(Config.setting.wechatConfig?.id as string, userId, [phoneNumber])
    ChatStateStore.update(chatId, {
      userSlots: {
        phoneNumber: phoneNumber
      }
    })

    // 更新 客户标签
    await DataService.updateTags(userId, `${courseNo}期`)

    await ChatDB.updateMoerIdAndCourseNo(chatId, moerId, courseNo)

    // 表单挖需
    await FlowTask.schedule(
      FlowTaskType.FormIntentionQuery,
      chatId,
      5 * 60 * 1000,
      { userId, phoneNumber },
      {
        auto_retry: true,
        independent: true
      }
    )

    // 判断是否重复购课，重复购课，走一下退费流程。不要进行拉群，私聊任务创建
    if (!Config.isInternalMember(userId) && await DataService.hasPurchasedIntroCourseBefore(moerId)) {
      await sleep(5 * 1000)
      await MessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: '看到咱们之前已经下单过冥想入门营了，其实咱们内容都是一样的哈。可以直接查看之前的回放哈，这期的报名费班班帮您申请退款吧。'
      })
      await sleep(8 * 1000)

      await MessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: '您可以拿下单手机号登我们小程序，在「学习」那里就可以观看之前的回放啦'
      })

      await MessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: '[上课小程序]',
        send_msg: {
          type: IWecomMsgType.MiniProgram,
          appId: 'gh_7e79cc9a6e81@app',
          description: 'More冥想',
          pagePath: 'pages/meditation/index.html?pid=781966&_um_ssrc=oGloS5MStt_0tPXvRc_bHFazogA8,oGloS5HSQV_bLwY0H9pkQUP_ozcQ&_um_sts=1733379864157',
          thumbUrl: 'https://workmate.s3.cn-northwest-1.amazonaws.com.cn/link_msg/64b30127-85f1-4962-93a9-b31c1e426702/60d8a142-9225-4aa7-a54c-ada5e0443a6c.jpg',
          title: '墨尔冥想',
          username: 'wxb944dac25d627482',
          iconUrl: 'http://mmbiz.qpic.cn/mmbiz_png/ELJnCVnUFFicdsWiaU24SEXic4zzgXAEJIR8yMwEpoeNZibulErNbicIiaibzVQkApzQt77uZyWia5xic4ZaD4Jt3P4EoHA/640?wx_fmt=png&wxfrom=200'
        }
      })

      await HumanTransfer.transfer(chatId, userId, HumanTransferType.RefundCourse)
      return false
    }

    const currentTime = await DataService.getCurrentTime(chatId)

    // 实际期数不匹配，上课时间修正
    if (courseNo !== DataService.getNextWeekCourseNo()) {
      const startTime = await DataService.getCourseStartTimeByCourseNo(courseNo)
      const startTimeStr = `${ startTime.getMonth() + 1 }月${  startTime.getDate()  }日`
      const liveLink = await DataService.getCourseLinkByCourseNo(courseNo, 0)
      await sleep(5 * 1000)
      await MessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: `不好意思同学，老师刚才看错了，咱们10分钟小讲堂的课程链接是${liveLink}，这个课程是【（${startTimeStr}）20:00】开始哈`
      })
      await sleep(5 * 1000)
    }


    // 如果是 周六，周日 进来的客户，直接拉群
    if ((!currentTime.is_course_week && currentTime.day === 5 && DateHelper.isTimeAfter(currentTime.time, '20:30:00')) || (!currentTime.is_course_week && currentTime.day >= 6) || (currentTime.is_course_week && currentTime.day === 1)) {
      await sleep(5 * 1000)

      await MessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: '咱们学习群已经建好 ，后续上课通知和分享都在群里，已经拉咱进群了',
      })
      await inviteToGroup(userId, 5000)
    }

    // await addPreCourseCheckTask(chatId, userId)

    if (Config.setting.localTest || Config.setting.eventForward) {
      await startTasks(userId, chatId) // 创建任务
    } else {
      startTasks(userId, chatId) // 创建任务
    }

    await DataService.saveChat(chatId, userId)

    return true
  }

  public static async formIntentionQuery(chatId: string, userId: string) {
    const isSendIntentionQuery = ChatStateStore.get(chatId).state.is_send_user_query1

    if (!isSendIntentionQuery) {
      await MessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: '唐宁老师希望收集大家的需求以调整课程内容',
        send_msg: {
          type: IWecomMsgType.Voice,
          duration: 14,
          voiceUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/tongtongVoice/%e6%8c%96%e9%9c%801%e8%af%ad%e9%9f%b3.silk'
        }
      })

      await sleep(2000)

      await MessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: `🙆🏻‍♀️您目前的生活角色？
1-职场奋斗者
2-家庭管理者
3-退休精进者
4-修行者

🐣您的冥想经验值？
5-纯小白
6-接触过
7-有基础

🎯最想点亮的人生议题
8-情绪减压
9-专注提升
10-睡眠改善
11-财富能量
12-亲密关系
13-灵性成长

——————
回复班班数字就好，例如：1-5-8，班班统计好，课前给老师参考`,
      })

      ChatStateStore.update(chatId, { state: { is_send_user_query1: true } })

      // 15min不回复
      await FlowTask.schedule(
        FlowTaskType.FormIntentionQueryReminder,
        chatId,
        15 * 60 * 1000,
        { userId }
      )

    }
  }

  public static async updateUserSource(chatId: string, userId: string, phoneNumber: string) {
    const moerUser = await MoerAPI.getUserByPhone(phoneNumber)

    const source = moerUser.source

    if (source.length === 0) {
      logger.trace({ chat_id: chatId },  `绑定客户渠道来源失败返回结果：${JSON.stringify(moerUser)}`)
      return
    }

    await DataService.updateTags(userId, source)

    ChatStateStore.update(chatId, {
      userSlots: {
        source: source,
      }
    })
  }
}