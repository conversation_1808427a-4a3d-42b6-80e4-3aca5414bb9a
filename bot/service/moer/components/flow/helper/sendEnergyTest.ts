import { FlowTask } from '../../schedule/silent_requestion'
import { ChatStateStore } from '../../../storage/chat_state_store'


//  10 mins 后客户没有消息，进入到发送能量测评，注意客户一旦说话，任务会被取消
export async function sendEnergyTest(chat_id: string, user_id: string) {
  if (ChatStateStore.getFlags(chat_id).is_delayed_send_energy_test) {
    return
  }


  await FlowTask.schedule(
    'delayed_energy_test',
    chat_id,
    50 * 60 * 1000,
    { user_id },
    {
      auto_retry: true,
      independent: true
    }
  )
}