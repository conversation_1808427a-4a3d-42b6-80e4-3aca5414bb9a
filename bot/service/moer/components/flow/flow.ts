import { ChatStateStore, WealthOrchardStore } from '../../storage/chat_state_store'
import { ChatHistoryService } from '../chat_history/chat_history'
import { MoerNodeMap } from './nodes'
import { ChatInterruptHandler } from '../message/interrupt_handler'
import logger from '../../../../model/logger/logger'
import { MoerNode } from './nodes/type'
import { Router } from './nodes/router'
import { DataService } from '../../getter/getData'
import { AsyncLock } from '../../../../lib/lock/lock'
import { sendEnergyTest } from './helper/sendEnergyTest'
import { getState } from './schedule/task/baseTask'
import { sleep } from '../../../../lib/schedule/schedule'
import { MemoryStore } from '../memory/memory_store'
import { Planner } from '../planner'
import { FlowTask } from '../schedule/silent_requestion'
import { FlowTaskType } from '../schedule/silent_reask_tasks'

export interface IWorkflowState {
    chat_id: string
    user_id: string
    userMessage: string

    interruptHandler: ChatInterruptHandler // 用于辅助打断当前对话，同时只执行一个回复，避免重复
    round_id: string // 用来区分每轮对话的 id
}

export class WorkFlow {
  /**
   * 对话流程
   * @param chat_id
   * @param user_id
   * @param userMessage
   */
  public static async step(chat_id: string, user_id: string, userMessage: string) {
    userMessage = this.transferWechatEmoji(userMessage)
    await ChatHistoryService.addUserMessage(chat_id, userMessage)
    if (userMessage === '[表情]') {
      return // 表情消息 暂不做处理
    }

    const entryNode = ChatStateStore.get(chat_id).nextStage

    const lock = new AsyncLock()

    const state = await getState(chat_id, user_id, userMessage) // 如果有新消息，在锁释放后，当前流程会中断

    await lock.acquire(chat_id, async () => { // 如果有新消息，当前回复会被丢弃
      await WorkFlow.run(entryNode as MoerNode, state)
    }, { timeout: 2 * 60 * 1000 })
  }

  private static async run(entryNode: MoerNode, state: IWorkflowState) {
    let node = MoerNodeMap.get(entryNode)
    logger.trace({ chat_id: state.chat_id }, `初始跳转节点: ${entryNode}`)
    if (!node) {
      logger.error(`[MoerFlow] node not found: ${entryNode}`)
      return
    }

    // 异步添加 Memory, 客户槽位
    MemoryStore.pushRecentMemoryToVectorDB(state.chat_id, state.round_id)

    // 根据客户消息自动转移
    const autoTransferNode = await Router.route(state)
    if (autoTransferNode === MoerNode.DummyEnd) {
      return
    }

    if (autoTransferNode && autoTransferNode !== MoerNode.Dummy) {
      logger.trace({ chat_id: state.chat_id }, `重定向到节点: ${autoTransferNode}`)

      node = MoerNodeMap.get(autoTransferNode)
      if (!node) {
        logger.error(`[MoerFlow] auto transfer node not found: ${autoTransferNode}`)
        return
      }
    }

    if (autoTransferNode !== MoerNode.WealthOrchardAnalyze) { // 财富果园未处理完，将消息进行延迟处理，等待财富果园处理结束后处理
      if (WealthOrchardStore.getUserMessages(state.chat_id).length > 0) {
        await sleep(5 * 60 * 1000) // 5分钟后再进行处理
        // 把当前消息挪到最后作为新消息处理，防止受到财富果园的影响
        await ChatHistoryService.moveToEnd(state.chat_id, state.userMessage)
      }
    }

    const nextStage = await node.invoke(state)
    ChatStateStore.update(state.chat_id, { nextStage })

    await this.postReply(state)
  }

  private static async postReply(state: IWorkflowState) {
    // 补充发送能量测评
    await this.sendEnergyTest(state)

    // 运行沉默检测
    await FlowTask.schedule(
      FlowTaskType.SilenceDetection,
      state.chat_id,
      10 * 60 * 1000,
      undefined,
      {
        auto_retry: true
      }
    )
  }

  private static transferWechatEmoji(message: string) {
    const emojiRegex = /\[.*?\]/g
    const emojiMap = {
      '[微笑]':'😊',
      '[调皮]': '😝',
      '[合十]': '🙏',
      '[爱心]': '💗',
      '[玫瑰]': '🌹',
      '[捂脸]': '🤦',
      '[笑哭]': ' 😂',
      '[咖啡]': '☕️',
      '[抱拳]': '🙏',
      '[拥抱]':'🫂'
    }
    return message.replace(emojiRegex, (match) => {
      const emoji = emojiMap[match]
      return emoji || match
    })
  }

  private static async sendEnergyTest(state: IWorkflowState) {
    // 上课周 周一 晚 7点后不再发送
    const currentTime = await DataService.getCurrentTime(state.chat_id)
    if ((currentTime.is_course_week) && ((currentTime.day >= 2) || (currentTime.day === 1 && new Date().getHours() >= 19))) {
      return
    }

    if (ChatStateStore.get(state.chat_id).state.is_send_pre_course_completion_gift) {
      await sendEnergyTest(state.chat_id, state.user_id)
    }
  }
}