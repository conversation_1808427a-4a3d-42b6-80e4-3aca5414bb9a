/**
 * 测试好友接受事件处理
 * 使用方法: ts-node test/tools/test_friend_accepted.ts
 */

import { JuziEvent } from '../../bot_starter/handler/juzi_event'
import { ChatStateStore, ChatStatStoreManager } from '../../bot/service/moer/storage/chat_state_store'
import { ChatDB } from '../../bot/service/moer/database/chat'

async function testFriendAcceptedEvent() {
  console.log('🧪 开始测试好友接受事件处理...\n')
  
  // 模拟一个FriendAcceptedEvent
  const mockEvent = {
    imContactId: 'test-contact-' + Date.now(),
    name: '测试用户' + Date.now(),
    avatar: 'https://thispersondoesnotexist.com/image', // 使用一个真实的头像URL
    gender: 1, // 1-男
    createTimestamp: Date.now(),
    imInfo: {
      externalUserId: 'external-user-' + Date.now(),
      followUser: {
        wecomUserId: 'wecom-user-' + Date.now()
      }
    },
    botInfo: {
      botId: 'bot-' + Date.now(),
      imBotId: 'im-bot-' + Date.now(),
      name: '墨尔冥想助手',
      avatar: 'https://example.com/bot-avatar.jpg'
    }
  }

  console.log('📝 模拟事件数据:')
  console.log(JSON.stringify(mockEvent, null, 2))

  try {
    console.log('\n🔍 验证事件格式...')
    const isValid = JuziEvent.isFriendAcceptedEvent(mockEvent)
    console.log('格式验证结果:', isValid ? '✅ 通过' : '❌ 失败')

    if (!isValid) {
      console.log('❌ 事件格式验证失败，停止测试')
      return
    }

    console.log('\n🚀 处理好友接受事件...')
    await JuziEvent.handle(mockEvent)

    console.log('\n✅ 事件处理完成，检查结果...')

    // 检查结果
    const chatId = `${mockEvent.imContactId}_test`
    
    // 从数据库检查
    const chatFromDB = await ChatDB.getById(chatId)
    if (chatFromDB) {
      console.log('\n📊 数据库中的结果:')
      console.log('- 客户名称:', chatFromDB.contact.wx_name)
      console.log('- userSlots:', JSON.stringify(chatFromDB.chat_state.userSlots, null, 2))
      console.log('- state flags:', JSON.stringify(chatFromDB.chat_state.state, null, 2))
    } else {
      console.log('❌ 数据库中没有找到聊天记录')
    }

    // 从内存检查
    await ChatStatStoreManager.initState(chatId, true)
    const chatState = ChatStateStore.get(chatId)
    
    console.log('\n🧠 内存中的结果:')
    console.log('- userSlots:', JSON.stringify(chatState.userSlots, null, 2))
    console.log('- state flags:', JSON.stringify(chatState.state, null, 2))

    console.log('\n🎯 新增字段检查:')
    console.log('- avatar_analysis:', chatState.userSlots.avatar_analysis || '未设置')
    console.log('- gender:', chatState.userSlots.gender || '未设置')
    console.log('- wx_nickname:', chatState.userSlots.wx_nickname || '未设置')
    console.log('- is_friend_accepted:', chatState.state.is_friend_accepted || false)

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error)
  }
}

async function checkExistingCustomer() {
  console.log('\n🔍 检查现有客户的数据...\n')
  
  const chatId = '7881301056349177_1688858254705213'
  
  try {
    // 从数据库检查
    const chatFromDB = await ChatDB.getById(chatId)
    if (chatFromDB) {
      console.log('📊 数据库中的数据:')
      console.log('- 客户名称:', chatFromDB.contact.wx_name)
      console.log('- 创建时间:', chatFromDB.created_at.toLocaleString())
      console.log('- userSlots:', JSON.stringify(chatFromDB.chat_state.userSlots, null, 2))
      console.log('- state flags:', JSON.stringify(chatFromDB.chat_state.state, null, 2))
    } else {
      console.log('❌ 数据库中没有找到该聊天记录')
      return
    }

    // 强制从数据库重新初始化状态
    await ChatStatStoreManager.initState(chatId, true)
    
    // 获取内存中的状态
    const chatState = ChatStateStore.get(chatId)
    
    console.log('\n🧠 内存中的数据:')
    console.log('- userSlots:', JSON.stringify(chatState.userSlots, null, 2))
    console.log('- state flags:', JSON.stringify(chatState.state, null, 2))
    
    console.log('\n🎯 新增字段检查:')
    console.log('- avatar_analysis:', chatState.userSlots.avatar_analysis || '❌ 未设置')
    console.log('- gender:', chatState.userSlots.gender || '❌ 未设置')
    console.log('- wx_nickname:', chatState.userSlots.wx_nickname || '❌ 未设置')
    console.log('- is_friend_accepted:', chatState.state.is_friend_accepted ? '✅ 是' : '❌ 否')

    // 如果字段缺失，尝试手动更新
    if (!chatState.userSlots.avatar_analysis && !chatState.userSlots.gender && !chatState.userSlots.wx_nickname) {
      console.log('\n🔧 检测到字段缺失，尝试手动更新...')
      
      ChatStateStore.update(chatId, {
        userSlots: {
          gender: '未知',
          wx_nickname: chatFromDB.contact.wx_name,
          avatar_analysis: '手动添加的测试数据'
        }
      })
      
      // 保存到数据库
      await ChatDB.updateState(chatId, ChatStateStore.get(chatId))
      
      console.log('✅ 手动更新完成')
    }

  } catch (error) {
    console.error('❌ 检查过程中出现错误:', error)
  }
}

async function main() {
  console.log('🚀 好友接受事件测试工具启动\n')
  
  // 检查现有客户
  await checkExistingCustomer()
  
  // 测试新的好友接受事件（可选）
  // await testFriendAcceptedEvent()
  
  console.log('\n✨ 测试完成！')
  process.exit(0)
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
  main().catch(console.error)
}

export { testFriendAcceptedEvent, checkExistingCustomer }
