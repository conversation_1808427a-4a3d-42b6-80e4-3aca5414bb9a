/**
 * 修复现有客户的用户画像数据
 * 使用方法: ts-node test/tools/fix_existing_customer.ts
 */

import { ChatStateStore, ChatStatStoreManager } from '../../bot/service/moer/storage/chat_state_store'
import { ChatDB } from '../../bot/service/moer/database/chat'
import { LLM } from '../../bot/lib/ai/llm/LLM'

async function fixExistingCustomer(chatId: string) {
  console.log(`🔧 开始修复客户数据: ${chatId}\n`)
  
  try {
    // 1. 从数据库获取客户信息
    const chat = await ChatDB.getById(chatId)
    if (!chat) {
      console.log('❌ 数据库中没有找到该聊天记录')
      return
    }

    console.log('📊 客户信息:')
    console.log('- 客户名称:', chat.contact.wx_name)
    console.log('- 微信ID:', chat.contact.wx_id)
    console.log('- 创建时间:', chat.created_at.toLocaleString())

    // 2. 初始化状态
    await ChatStatStoreManager.initState(chatId, true)
    const chatState = ChatStateStore.get(chatId)

    console.log('\n📋 当前用户画像:')
    console.log(JSON.stringify(chatState.userSlots, null, 2))

    // 3. 检查是否需要修复
    const needsAvatarAnalysis = !chatState.userSlots.avatar_analysis
    const needsGender = !chatState.userSlots.gender
    const needsNickname = !chatState.userSlots.wx_nickname

    if (!needsAvatarAnalysis && !needsGender && !needsNickname) {
      console.log('✅ 客户数据完整，无需修复')
      return
    }

    console.log('\n🔍 检测到缺失字段:')
    if (needsAvatarAnalysis) console.log('- ❌ avatar_analysis')
    if (needsGender) console.log('- ❌ gender')
    if (needsNickname) console.log('- ❌ wx_nickname')

    // 4. 准备更新数据
    const updateData: any = {}

    // 添加微信昵称
    if (needsNickname) {
      updateData.wx_nickname = chat.contact.wx_name
      console.log(`\n📝 添加微信昵称: ${chat.contact.wx_name}`)
    }

    // 添加默认性别（如果没有其他信息的话）
    if (needsGender) {
      updateData.gender = '未知'
      console.log('👤 添加默认性别: 未知')
    }

    // 添加头像分析（如果有头像URL的话）
    if (needsAvatarAnalysis) {
      // 这里我们添加一个占位符，实际的头像分析需要头像URL
      updateData.avatar_analysis = '【系统补充】客户头像信息暂未分析'
      console.log('🖼️  添加头像分析占位符')
    }

    // 5. 更新用户画像
    ChatStateStore.update(chatId, {
      userSlots: updateData
    })

    // 6. 保存到数据库
    await ChatDB.updateState(chatId, ChatStateStore.get(chatId))

    console.log('\n✅ 修复完成！')

    // 7. 验证修复结果
    await ChatStatStoreManager.initState(chatId, true)
    const updatedChatState = ChatStateStore.get(chatId)

    console.log('\n📊 修复后的用户画像:')
    console.log(JSON.stringify(updatedChatState.userSlots, null, 2))

    console.log('\n🎯 字段检查:')
    console.log('- avatar_analysis:', updatedChatState.userSlots.avatar_analysis || '❌ 未设置')
    console.log('- gender:', updatedChatState.userSlots.gender || '❌ 未设置')
    console.log('- wx_nickname:', updatedChatState.userSlots.wx_nickname || '❌ 未设置')

  } catch (error) {
    console.error('❌ 修复过程中出现错误:', error)
  }
}

async function analyzeAvatarForCustomer(chatId: string, avatarUrl: string) {
  console.log(`🖼️  开始为客户分析头像: ${chatId}\n`)
  
  try {
    // 头像分析prompt
    const avatarAnalysisPrompt = `你是一名专业的头像分析师，请帮我对客户的头像进行分析。

## 分析方向：
1. 如果头像是真人图片，请以【真人照片】开头，然后判断出客户的性别和大概的年龄区间。另外大概描述一下人物形象和风格。
2. 如果头像是二次元/动漫风格的图片则以【动漫头像】开头，然后描述一下头像风格。
3. 如果头像是风景图片则以【风景】开头，然后描述图片内容`

    console.log('🤖 正在调用LLM分析头像...')
    const avatarAnalysis = await new LLM({ max_tokens: 300 }).imageChat(avatarUrl, avatarAnalysisPrompt)

    console.log('📝 头像分析结果:')
    console.log(avatarAnalysis)

    // 更新用户画像
    await ChatStatStoreManager.initState(chatId, true)
    ChatStateStore.update(chatId, {
      userSlots: {
        avatar_analysis: avatarAnalysis
      }
    })

    // 保存到数据库
    await ChatDB.updateState(chatId, ChatStateStore.get(chatId))

    console.log('\n✅ 头像分析完成并已保存')

  } catch (error) {
    console.error('❌ 头像分析失败:', error)
  }
}

async function main() {
  console.log('🚀 客户数据修复工具启动\n')
  
  // 修复特定客户
  const targetChatId = '7881301056349177_1688858254705213'
  await fixExistingCustomer(targetChatId)
  
  // 如果你有头像URL，可以取消注释下面的代码进行头像分析
  // const avatarUrl = 'https://example.com/avatar.jpg'
  // await analyzeAvatarForCustomer(targetChatId, avatarUrl)
  
  console.log('\n✨ 修复完成！')
  process.exit(0)
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
  main().catch(console.error)
}

export { fixExistingCustomer, analyzeAvatarForCustomer }
