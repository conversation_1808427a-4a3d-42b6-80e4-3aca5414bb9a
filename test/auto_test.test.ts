import { Client } from 'langsmith'
import { DataService } from '../bot/service/moer/getter/getData'
import dayjs from 'dayjs'

describe('Test', function () {
  beforeAll(() => {

  })

  it('course', async () => {
    // const currentTime = await DataService.getCurrentTime('7881303260963350_1688856322643146')
    //
    // // 周一之后不发送完课礼了
    // if ((currentTime.is_course_week) && ((currentTime.day >= 2) || (currentTime.day === 1 && new Date().getHours() >= 19))) {
    //   console.log('hi')
    // }
    const courseStartTime = await DataService.getCourseStartTime('7881303260963350_1688856322643146')
    const formatCourseStartTime = dayjs(courseStartTime).format('M月D')

    console.log(formatCourseStartTime)
  }, 60000)

  it('should pass', async () => {
    const client = new Client()
    const key = 'promptName'
    const value = 'MergeSlotArray'

    const runs = client.listRuns({
      projectName: 'moer',
      filter: `and(eq(metadata_key, '${key}'), eq(metadata_value, '${value}'))`,
      error: false
    })

    // 利用 PromptTemplate 解析 输入参数
    for await (const run of runs) {
      if (run.name === 'StrOutputParser') continue

      if (run.name === 'AzureChatOpenAI') {
        const input = run.inputs.messages[0][0].kwargs.content
        const output = run.outputs?.generations[0][0].text
        console.log(input, output)
      }
    }
  }, 60000)
})